target("dcim_macro")
    set_kind("static")
    set_languages("cxx11")
    add_includedirs("./inc",{public = true})
    add_files("./src/*.c|main.c")
    add_files("./src/*.cpp")

target("test_float")
    set_kind("binary")
    add_files("./test/test_float.cpp")
    add_includedirs("./test",{public = true})
    add_deps("ac_types")
    add_deps("dcim_macro")

    add_tests("wt_fp16", {files = "./test/test_float.cpp", defines = "TEST_WT_FP16"})
    add_tests("wt_bf16", {files = "./test/test_float.cpp", defines = "TEST_WT_BF16"})
    add_tests("wt_bbf16", {files = "./test/test_float.cpp", defines = "TEST_WT_BBF16"})
    add_tests("wt_fp8e4", {files = "./test/test_float.cpp", defines = "TEST_WT_FP8E4"})
    add_tests("wt_fp8e5", {files = "./test/test_float.cpp", defines = "TEST_WT_FP8E5"})
    add_tests("wt_int16", {files = "./test/test_float.cpp", defines = "TEST_WT_INT16"})
    add_tests("wt_int8", {files = "./test/test_float.cpp", defines = "TEST_WT_INT8"})
    add_tests("wt_int4", {files = "./test/test_float.cpp", defines = "TEST_WT_INT4"})
    set_group("cim_macro")

target("test_compatibility")
    set_kind("binary")
    add_files("./test/test_compatibility.cpp")
    add_includedirs("./test",{public = true})
    add_deps("dcim_macro")
    set_group("cim_macro")


target("test_debug")
    set_kind("binary")
    add_files("./test/test_debug.cpp")
    add_includedirs("./test",{public = true})
    add_deps("dcim_macro")
    set_group("cim_macro")

target("test_all_formats")
    set_kind("binary")
    add_files("./test/test_all_formats.cpp")
    add_includedirs("./test",{public = true})
    add_deps("dcim_macro")
    set_group("cim_macro")

target("test_fp8_debug")
    set_kind("binary")
    add_files("./test/test_fp8_debug.cpp")
    add_includedirs("./test",{public = true})
    add_deps("dcim_macro")
    set_group("cim_macro")

target("test_optimized_debug")
    set_kind("binary")
    add_files("./test/test_optimized_debug.cpp")
    add_includedirs("./test",{public = true})
    add_deps("dcim_macro")
    set_group("cim_macro")




