#include "ac_std_float.h"
#include "ac_int.h"

typedef ac_int<16, true> ac_int16_t;
typedef ac_int<8, true> ac_int8_t;
typedef ac_int<4, true> ac_int4_t;

typedef ac_std_float<16, 5> ac_float16_t;
typedef ac_std_float<16, 8> ac_bfloat16_t;
typedef ac_std_float<16, 1> ac_bbfloat16_t;
typedef ac_std_float<8,4> ac_fp8e4_t;
typedef ac_std_float<8,5> ac_fp8e5_t;





// #include <spdlog/fmt/fmt.h>
// #include <fmt/core.h>

// namespace fmt {
// template <>
// struct formatter<ac_int16_t> {
//   constexpr auto parse(format_parse_context& ctx) -> decltype(ctx.begin()) {
//     return ctx.end();
//   }

//   template <typename FormatContext>
//   auto format(const ac_int16_t& val, FormatContext& ctx) const -> decltype(ctx.out()) {
//     return format_to(ctx.out(), "{}", val.to_int());
//   }
// };

// template <>
// struct formatter<ac_int8_t> {
//   constexpr auto parse(format_parse_context& ctx) -> decltype(ctx.begin()) {
//     return ctx.end();
//   }

//   template <typename FormatContext>
//   auto format(const ac_int8_t& val, FormatContext& ctx) const -> decltype(ctx.out()) {
//     return format_to(ctx.out(), "{}", val.to_int());
//   }
// };

// template <>
// struct formatter<ac_int4_t> {
//   constexpr auto parse(format_parse_context& ctx) -> decltype(ctx.begin()) {
//     return ctx.end();
//   }

//   template <typename FormatContext>
//   auto format(const ac_int4_t& val, FormatContext& ctx) const -> decltype(ctx.out()) {
//     return format_to(ctx.out(), "{}", val.to_int());
//   }
// };

// template <>
// struct formatter<ac_float16_t> {
//   constexpr auto parse(format_parse_context& ctx) -> decltype(ctx.begin()) {
//     return ctx.end();
//   }

//   template <typename FormatContext>
//   auto format(const ac_float16_t& val, FormatContext& ctx) const -> decltype(ctx.out()) {
//     return format_to(ctx.out(), "{}", val.to_float());
//   }
// };

// template <>
// struct formatter<ac_bfloat16_t> {
//   constexpr auto parse(format_parse_context& ctx) -> decltype(ctx.begin()) {
//     return ctx.end();
//   }

//   template <typename FormatContext>
//   auto format(const ac_bfloat16_t& val, FormatContext& ctx) const -> decltype(ctx.out()) {
//     return format_to(ctx.out(), "{}", val.to_float());
//   }
// };

// template <>
// struct formatter<ac_bbfloat16_t> {
//   constexpr auto parse(format_parse_context& ctx) -> decltype(ctx.begin()) {
//     return ctx.end();
//   }

//   template <typename FormatContext>
//   auto format(const ac_bbfloat16_t& val, FormatContext& ctx) const -> decltype(ctx.out()) {
//     return format_to(ctx.out(), "{}", val.to_float());
//   }
// };

// template <>
// struct formatter<ac_fp8e4_t> {
//   constexpr auto parse(format_parse_context& ctx) -> decltype(ctx.begin()) {
//     return ctx.end();
//   }

//   template <typename FormatContext>
//   auto format(const ac_fp8e4_t& val, FormatContext& ctx) const -> decltype(ctx.out()) {
//     return format_to(ctx.out(), "{}", val.to_float());
//   }
// };

// template <>
// struct formatter<ac_fp8e5_t> {
//   constexpr auto parse(format_parse_context& ctx) -> decltype(ctx.begin()) {
//     return ctx.end();
//   }

//   template <typename FormatContext>
//   auto format(const ac_fp8e5_t& val, FormatContext& ctx) const -> decltype(ctx.out()) {
//     return format_to(ctx.out(), "{}", val.to_float());
//   }
// };
// }  // namespace fmt
