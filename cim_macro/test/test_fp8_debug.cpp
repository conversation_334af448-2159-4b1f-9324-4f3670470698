#include "../inc/fp_prealign.hpp"
#include "../inc/dcim_com.h"
#include "../inc/weight_table.h"
#include <iostream>
#include <iomanip>

int main() {
    std::cout << "=== FP8E4M3 Debug Analysis ===" << std::endl;
    
    // Test the specific failing cases
    int failing_vectors[] = {11, 12, 16, 20, 24, 30};
    int failing_indices[][5] = {
        {2, -1, -1, -1, -1},      // Vector 11
        {18, -1, -1, -1, -1},     // Vector 12
        {31, -1, -1, -1, -1},     // Vector 16
        {13, 24, -1, -1, -1},     // Vector 20
        {19, -1, -1, -1, -1},     // Vector 24
        {19, -1, -1, -1, -1}      // Vector 30
    };
    
    uint16_t cpp_result[33];
    uint16_t c_result[33];
    
    for (int v = 0; v < 6; ++v) {
        int vec = failing_vectors[v];
        std::cout << "\n--- Vector " << vec << " ---" << std::endl;
        
        // Call both implementations
        float_data_align_cpp(const_cast<uint16_t*>(wt_mhex_fp8e4[vec]), FP8E4, cpp_result);
        float_data_align(const_cast<uint16_t*>(wt_mhex_fp8e4[vec]), FP8E4, c_result);
        
        // Check each failing index
        for (int j = 0; j < 5 && failing_indices[v][j] != -1; ++j) {
            int idx = failing_indices[v][j];
            uint16_t input_val = wt_mhex_fp8e4[vec][idx];
            uint16_t expected = wt_algn_fp8e4[vec][idx];
            uint16_t cpp_got = cpp_result[idx];
            uint16_t c_got = c_result[idx];
            
            std::cout << "Index " << idx << ": Input=0x" << std::hex << input_val
                      << " Expected=0x" << expected
                      << " C++_Got=0x" << cpp_got
                      << " C_Got=0x" << c_got << std::dec << std::endl;
            
            // Analyze the input value
            uint8_t sign = (input_val >> 7) & 1;
            uint8_t exp = (input_val >> 3) & 0xF;
            uint8_t mantissa = input_val & 0x7;
            
            std::cout << "  Input analysis: Sign=" << (int)sign 
                      << " Exp=" << (int)exp 
                      << " Mantissa=" << (int)mantissa;
            
            if (exp == 0 && mantissa == 0) {
                std::cout << " (This is " << (sign ? "-0" : "+0") << ")";
            }
            std::cout << std::endl;
            
            // Check if this is the -0/+0 case
            if (expected == 0x80 && cpp_got == 0x0) {
                std::cout << "  This is the -0/+0 discrepancy case!" << std::endl;
            }
        }
    }
    
    std::cout << "\n=== Analysis Summary ===" << std::endl;
    std::cout << "All failing cases involve Expected=0x80 vs Got=0x0" << std::endl;
    std::cout << "This corresponds to -0 vs +0 in FP8E4M3 format" << std::endl;
    std::cout << "According to the comment in weight_table.h, this is a known" << std::endl;
    std::cout << "difference where 0x80=-0 and 0x00=+0 are mathematically equivalent" << std::endl;
    
    return 0;
}
