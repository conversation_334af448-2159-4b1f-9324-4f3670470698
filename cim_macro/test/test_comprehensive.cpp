#include "../inc/fp_prealign.hpp"
#include "../inc/dcim_com.h"
#include "../inc/weight_table.h"
#include "../inc/input_table.h"
#include <iostream>
#include <iomanip>
#include <cstring>

/**
 * @brief Test FP16 format with real test data
 */
int test_fp16_format() {
    std::cout << "=== Testing FP16 Format ===" << std::endl;

    uint16_t cpp_result[33];
    uint16_t c_result[33];
    bool all_passed = true;

    // Test with first few weight vectors from the test data
    for (int vec = 0; vec < 5 && vec < 16; ++vec) {  // Test first 5 vectors
        // Call C++ implementation
        float_data_align_cpp(const_cast<uint16_t*>(wt_mhex_fp16[vec]), FP16, cpp_result);

        // Call original C implementation
        float_data_align(const_cast<uint16_t*>(wt_mhex_fp16[vec]), FP16, c_result);

        // Compare with expected results
        bool vector_passed = true;
        for (int i = 0; i < 33; ++i) {
            if (cpp_result[i] != wt_algn_fp16[vec][i]) {
                vector_passed = false;
                all_passed = false;
                std::cout << "FP16 Vector " << vec << " Index " << i
                          << ": Expected=0x" << std::hex << wt_algn_fp16[vec][i]
                          << ", Got=0x" << cpp_result[i] << std::dec << std::endl;
            }

            // Also check C implementation matches expected
            if (c_result[i] != wt_algn_fp16[vec][i]) {
                std::cout << "C implementation mismatch at Vector " << vec << " Index " << i
                          << ": Expected=0x" << std::hex << wt_algn_fp16[vec][i]
                          << ", C Got=0x" << c_result[i] << std::dec << std::endl;
            }
        }

        if (vector_passed) {
            std::cout << "✓ FP16 Vector " << vec << " PASSED" << std::endl;
        } else {
            std::cout << "✗ FP16 Vector " << vec << " FAILED" << std::endl;
        }
    }

    return all_passed ? 0 : 1;
}

/**
 * @brief Test BF16 format with real test data
 */
int test_bf16_format() {
    std::cout << "\n=== Testing BF16 Format ===" << std::endl;

    uint16_t cpp_result[33];
    uint16_t c_result[33];
    bool all_passed = true;

    // Test with first few weight vectors from the test data
    for (int vec = 0; vec < 5 && vec < 16; ++vec) {  // Test first 5 vectors
        // Call C++ implementation
        float_data_align_cpp(const_cast<uint16_t*>(wt_mhex_bf16[vec]), BF16, cpp_result);

        // Call original C implementation
        float_data_align(const_cast<uint16_t*>(wt_mhex_bf16[vec]), BF16, c_result);

        // Compare with expected results
        bool vector_passed = true;
        for (int i = 0; i < 33; ++i) {
            if (cpp_result[i] != wt_algn_bf16[vec][i]) {
                vector_passed = false;
                all_passed = false;
                std::cout << "BF16 Vector " << vec << " Index " << i
                          << ": Expected=0x" << std::hex << wt_algn_bf16[vec][i]
                          << ", Got=0x" << cpp_result[i] << std::dec << std::endl;
            }
        }

        if (vector_passed) {
            std::cout << "✓ BF16 Vector " << vec << " PASSED" << std::endl;
        } else {
            std::cout << "✗ BF16 Vector " << vec << " FAILED" << std::endl;
        }
    }

    return all_passed ? 0 : 1;
}

/**
 * @brief Test edge cases and special values
 */
int test_edge_cases() {
    std::cout << "\n=== Testing Edge Cases ===" << std::endl;

    uint16_t test_data[32];
    uint16_t result[33];
    bool all_passed = true;

    // Test 1: All zeros
    std::cout << "Testing all zeros..." << std::endl;
    memset(test_data, 0, sizeof(test_data));
    float_data_align_cpp(test_data, FP16, result);
    std::cout << "✓ All zeros test completed" << std::endl;

    // Test 2: All ones (denormalized)
    std::cout << "Testing denormalized values..." << std::endl;
    for (int i = 0; i < 32; ++i) {
        test_data[i] = 0x0001;  // Smallest denormalized FP16
    }
    float_data_align_cpp(test_data, FP16, result);
    std::cout << "✓ Denormalized values test completed" << std::endl;

    // Test 3: Mixed positive and negative
    std::cout << "Testing mixed signs..." << std::endl;
    for (int i = 0; i < 32; ++i) {
        test_data[i] = (i % 2) ? 0x8000 : 0x0000;  // Alternating signs
    }
    float_data_align_cpp(test_data, FP16, result);
    std::cout << "✓ Mixed signs test completed" << std::endl;

    return 0;
}

int main() {
    std::cout << "=== Comprehensive Floating-Point Pre-alignment Tests ===" << std::endl;

    int result1 = test_fp16_format();
    int result2 = test_bf16_format();
    int result3 = test_edge_cases();

    if (result1 == 0 && result2 == 0 && result3 == 0) {
        std::cout << "\n✓ All comprehensive tests PASSED!" << std::endl;
        return 0;
    } else {
        std::cout << "\n✗ Some comprehensive tests FAILED!" << std::endl;
        return 1;
    }
}