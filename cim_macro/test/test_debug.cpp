#include "../inc/fp_prealign.hpp"
#include "../inc/dcim_com.h"
#include "../inc/weight_table.h"
#include <iostream>
#include <iomanip>
#include <cstring>

/**
 * @brief Debug function to analyze step-by-step calculation
 */
void debug_fp16_calculation(const uint16_t* input_data, int vector_index) {
    std::cout << "\n=== Debugging FP16 Vector " << vector_index << " ===" << std::endl;

    // Print input data
    std::cout << "Input data:" << std::endl;
    for (int i = 0; i < 32; ++i) {
        std::cout << "  [" << std::setw(2) << i << "] = 0x" << std::hex << std::setw(4) << std::setfill('0')
                  << input_data[i] << std::dec << std::endl;
    }

    // Step 1: Extract exponents and calculate regularized exponents
    std::cout << "\nStep 1: Exponent extraction and regularization" << std::endl;
    uint8_t reg_exp[32];
    uint8_t emax_src = 0;

    for (int i = 0; i < 32; ++i) {
        // Extract exponent bits (10-14 for FP16)
        uint8_t ebits = fp_prealign::BitUtils::extract_bits(10, 14, input_data[i]);
        uint8_t hbit = (ebits > 0) ? 1 : 0;

        // Regularize exponent (C implementation logic)
        reg_exp[i] = ebits & 0xfe;  // Clear LSB
        reg_exp[i] += hbit ? (ebits & 1) : 1;  // Add LSB or 1 for denormalized

        if (reg_exp[i] > emax_src) {
            emax_src = reg_exp[i];
        }

        std::cout << "  [" << std::setw(2) << i << "] ebits=0x" << std::hex << (int)ebits
                  << " hbit=" << (int)hbit << " reg_exp=0x" << (int)reg_exp[i] << std::dec << std::endl;
    }

    uint8_t emax_cmp = emax_src - 15;  // FP16 bias
    std::cout << "emax_src=0x" << std::hex << (int)emax_src << " emax_cmp=0x" << (int)emax_cmp << std::dec << std::endl;

    // Step 2: Process each element
    std::cout << "\nStep 2: Mantissa processing" << std::endl;
    uint16_t cpp_result[33];
    uint16_t c_result[33];

    // Call C implementation for comparison
    float_data_align(const_cast<uint16_t*>(input_data), FP16, c_result);

    for (int i = 0; i < 32; ++i) {
        // Extract sign bit
        uint8_t sbit = fp_prealign::BitUtils::extract_bits(15, 15, input_data[i]);

        // Extract mantissa bits (0-9 for FP16)
        uint16_t mantissa_bits = fp_prealign::BitUtils::extract_bits(0, 9, input_data[i]);

        // Construct mantissa with hidden bit (C implementation logic)
        uint8_t hbit = (fp_prealign::BitUtils::extract_bits(10, 14, input_data[i]) > 0) ? 1 : 0;
        uint16_t reg_manti = (hbit << 14) + (mantissa_bits << 4);

        // Right shift for alignment
        uint8_t shift_amount = emax_src - reg_exp[i];
        uint16_t aligned_mantissa = reg_manti >> shift_amount;

        // Two's complement conversion (C implementation logic)
        uint16_t final_mantissa = aligned_mantissa;
        if (sbit) {
            uint16_t bit_tmp = 0;
            for (int j = 0; j < 15; ++j) {
                bit_tmp += ((!((aligned_mantissa >> j) & 1)) << j);
            }
            final_mantissa = bit_tmp + 1;
        }

        // Final result with sign bit
        uint16_t final_result = (sbit << 15) + final_mantissa;
        cpp_result[i] = final_result;

        std::cout << "  [" << std::setw(2) << i << "] sbit=" << (int)sbit
                  << " mantissa=0x" << std::hex << mantissa_bits
                  << " hbit=" << std::dec << (int)hbit
                  << " reg_manti=0x" << std::hex << reg_manti
                  << " shift=" << std::dec << (int)shift_amount
                  << " aligned=0x" << std::hex << aligned_mantissa
                  << " final=0x" << final_mantissa
                  << " result=0x" << final_result
                  << " expected=0x" << wt_algn_fp16[vector_index][i]
                  << " c_impl=0x" << c_result[i] << std::dec;

        if (final_result != wt_algn_fp16[vector_index][i]) {
            std::cout << " *** MISMATCH ***";
        }
        std::cout << std::endl;
    }

    cpp_result[32] = emax_cmp;
    std::cout << "Public exponent: 0x" << std::hex << (int)emax_cmp
              << " expected: 0x" << (int)wt_algn_fp16[vector_index][32] << std::dec << std::endl;
}

/**
 * @brief Theoretical validation of a specific failing case
 */
void validate_theoretical_calculation() {
    std::cout << "\n=== Theoretical Validation ===" << std::endl;

    // Let's manually calculate Vector 0, Index 12 which is failing
    uint16_t test_value = wt_mhex_fp16[0][12];  // 0xa8c7
    std::cout << "Analyzing value 0x" << std::hex << test_value << std::dec << std::endl;

    // Extract components
    uint8_t sign = (test_value >> 15) & 1;
    uint8_t exponent = (test_value >> 10) & 0x1F;
    uint16_t mantissa = test_value & 0x3FF;

    std::cout << "Sign: " << (int)sign << std::endl;
    std::cout << "Exponent: 0x" << std::hex << (int)exponent << " (" << std::dec << (int)exponent << ")" << std::endl;
    std::cout << "Mantissa: 0x" << std::hex << mantissa << std::dec << std::endl;

    // Check if normalized or denormalized
    bool is_normalized = (exponent != 0);
    std::cout << "Is normalized: " << is_normalized << std::endl;

    // Calculate regularized exponent
    uint8_t reg_exp = exponent & 0xfe;
    reg_exp += is_normalized ? (exponent & 1) : 1;
    std::cout << "Regularized exponent: 0x" << std::hex << (int)reg_exp << std::dec << std::endl;

    // Expected result from test data
    uint16_t expected = wt_algn_fp16[0][12];  // 0x0000
    std::cout << "Expected result: 0x" << std::hex << expected << std::dec << std::endl;

    // This suggests the value should result in 0, which means either:
    // 1. The shift amount is >= 15 (complete right shift)
    // 2. The final mantissa calculation results in 0x8000 which gets converted to 0

    std::cout << "Analysis: Expected result is 0x0000, suggesting either complete right shift or special case handling" << std::endl;
}

int main() {
    std::cout << "=== Detailed Debugging Analysis ===" << std::endl;

    // Debug the failing cases
    debug_fp16_calculation(wt_mhex_fp16[0], 0);  // Vector 0 (has failure at index 12)

    validate_theoretical_calculation();

    return 0;
}