#include "../inc/fp_prealign.hpp"
#include "../inc/dcim_com.h"
#include "../inc/weight_table.h"
#include <iostream>
#include <iomanip>
#include <cstring>

int main() {
    std::cout << "=== Optimized Implementation Debug ===" << std::endl;
    
    // Test with first FP16 vector
    uint16_t cpp_result[33];
    uint16_t c_result[33];
    
    std::cout << "\n--- Testing FP16 Vector 0 ---" << std::endl;
    
    // Print input data
    std::cout << "Input data: ";
    for (int i = 0; i < 5; ++i) {
        std::cout << "0x" << std::hex << wt_mhex_fp16[0][i] << " ";
    }
    std::cout << "..." << std::dec << std::endl;
    
    // Call both implementations
    std::cout << "\nCalling float_data_align_cpp..." << std::endl;
    float_data_align_cpp(const_cast<uint16_t*>(wt_mhex_fp16[0]), FP16, cpp_result);
    
    std::cout << "Calling float_data_align (C)..." << std::endl;
    float_data_align(const_cast<uint16_t*>(wt_mhex_fp16[0]), FP16, c_result);
    
    // Print results
    std::cout << "\nResults comparison (first 10 values):" << std::endl;
    std::cout << "Index | Expected | C++_Got | C_Got" << std::endl;
    std::cout << "------|----------|---------|-------" << std::endl;
    
    for (int i = 0; i < 10; ++i) {
        std::cout << std::setw(5) << i << " | "
                  << "0x" << std::hex << std::setw(4) << std::setfill('0') << wt_algn_fp16[0][i] << " | "
                  << "0x" << std::setw(4) << std::setfill('0') << cpp_result[i] << " | "
                  << "0x" << std::setw(4) << std::setfill('0') << c_result[i] << std::dec << std::endl;
    }
    
    std::cout << "\nPublic exponent (index 32):" << std::endl;
    std::cout << "Expected: 0x" << std::hex << wt_algn_fp16[0][32] << std::endl;
    std::cout << "C++ Got:  0x" << cpp_result[32] << std::endl;
    std::cout << "C Got:    0x" << c_result[32] << std::dec << std::endl;
    
    // Test simple case
    std::cout << "\n--- Testing Simple Case ---" << std::endl;
    uint16_t simple_input[32];
    uint16_t simple_result[33];
    
    // All zeros except one value
    memset(simple_input, 0, sizeof(simple_input));
    simple_input[0] = 0x3C00;  // FP16: 1.0
    
    std::cout << "Input: simple_input[0] = 0x3C00 (FP16: 1.0), rest = 0" << std::endl;
    
    float_data_align_cpp(simple_input, FP16, simple_result);
    
    std::cout << "Results:" << std::endl;
    for (int i = 0; i < 5; ++i) {
        std::cout << "simple_result[" << i << "] = 0x" << std::hex << simple_result[i] << std::dec << std::endl;
    }
    std::cout << "Public exponent: 0x" << std::hex << simple_result[32] << std::dec << std::endl;
    
    return 0;
}
