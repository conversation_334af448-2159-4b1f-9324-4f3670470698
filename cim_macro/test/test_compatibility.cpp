#include "../inc/fp_prealign.hpp"
#include "../inc/dcim_com.h"
#include <iostream>
#include <iomanip>
#include <cstring>

/**
 * @brief Test compatibility between C++ and C implementations
 */
int test_compatibility() {
    // Test data: simple FP16 values
    uint16_t test_data[32];
    uint16_t cpp_result[33];
    uint16_t c_result[33];

    // Initialize test data with simple FP16 values
    for (int i = 0; i < 32; ++i) {
        test_data[i] = 0x3C00 + i;  // Simple FP16 values around 1.0
    }

    // Test FP16 format
    std::cout << "Testing FP16 compatibility..." << std::endl;

    // Call C++ implementation
    float_data_align_cpp(test_data, FP16, cpp_result);

    // Call original C implementation
    float_data_align(test_data, FP16, c_result);

    // Compare results
    bool fp16_match = true;
    for (int i = 0; i < 33; ++i) {
        if (cpp_result[i] != c_result[i]) {
            fp16_match = false;
            std::cout << "Mismatch at index " << i
                      << ": C++=" << std::hex << cpp_result[i]
                      << ", C=" << c_result[i] << std::dec << std::endl;
        }
    }

    if (fp16_match) {
        std::cout << "✓ FP16 compatibility test PASSED" << std::endl;
    } else {
        std::cout << "✗ FP16 compatibility test FAILED" << std::endl;
    }

    // Test BF16 format
    std::cout << "Testing BF16 compatibility..." << std::endl;

    // Initialize test data with simple BF16 values
    for (int i = 0; i < 32; ++i) {
        test_data[i] = 0x3F80 + i;  // Simple BF16 values around 1.0
    }

    // Call C++ implementation
    float_data_align_cpp(test_data, BF16, cpp_result);

    // Call original C implementation
    float_data_align(test_data, BF16, c_result);

    // Compare results
    bool bf16_match = true;
    for (int i = 0; i < 33; ++i) {
        if (cpp_result[i] != c_result[i]) {
            bf16_match = false;
            std::cout << "Mismatch at index " << i
                      << ": C++=" << std::hex << cpp_result[i]
                      << ", C=" << c_result[i] << std::dec << std::endl;
        }
    }

    if (bf16_match) {
        std::cout << "✓ BF16 compatibility test PASSED" << std::endl;
    } else {
        std::cout << "✗ BF16 compatibility test FAILED" << std::endl;
    }

    return (fp16_match && bf16_match) ? 0 : 1;
}

/**
 * @brief Test basic functionality of C++ classes
 */
int test_basic_functionality() {
    std::cout << "Testing basic C++ class functionality..." << std::endl;

    // Test FP16 class
    fp_prealign::FP16 fp16_val(0x3C00);  // 1.0 in FP16
    std::cout << "FP16 value 0x3C00:" << std::endl;
    std::cout << "  Sign: " << (int)fp16_val.sign() << std::endl;
    std::cout << "  Exponent: " << (int)fp16_val.exponent() << std::endl;
    std::cout << "  Mantissa: 0x" << std::hex << fp16_val.mantissa() << std::dec << std::endl;
    std::cout << "  Hidden bit: " << (int)fp16_val.hidden_bit() << std::endl;
    std::cout << "  Is denormalized: " << fp16_val.is_denormalized() << std::endl;

    // Test BF16 class
    fp_prealign::BF16 bf16_val(0x3F80);  // 1.0 in BF16
    std::cout << "BF16 value 0x3F80:" << std::endl;
    std::cout << "  Sign: " << (int)bf16_val.sign() << std::endl;
    std::cout << "  Exponent: " << (int)bf16_val.exponent() << std::endl;
    std::cout << "  Mantissa: 0x" << std::hex << bf16_val.mantissa() << std::dec << std::endl;
    std::cout << "  Hidden bit: " << (int)bf16_val.hidden_bit() << std::endl;
    std::cout << "  Is denormalized: " << bf16_val.is_denormalized() << std::endl;

    std::cout << "✓ Basic functionality test completed" << std::endl;
    return 0;
}

int main() {
    std::cout << "=== Floating-Point Pre-alignment Compatibility Tests ===" << std::endl;

    int result1 = test_basic_functionality();
    int result2 = test_compatibility();

    if (result1 == 0 && result2 == 0) {
        std::cout << "\n✓ All tests PASSED!" << std::endl;
        return 0;
    } else {
        std::cout << "\n✗ Some tests FAILED!" << std::endl;
        return 1;
    }
}