#ifndef FP_PREALIGN_HPP
#define FP_PREALIGN_HPP

#include <cstdint>
#include <type_traits>

namespace fp_prealign {

/**
 * @brief Bit manipulation utility functions for C++ implementation
 */
class BitUtils {
public:
    /**
     * @brief Extract bits from a value (equivalent to bit_acc function)
     * @param start_bit Starting bit position (inclusive)
     * @param stop_bit Stopping bit position (inclusive)
     * @param data Source data
     * @return Extracted bits
     */
    static constexpr uint32_t extract_bits(uint8_t start_bit, uint8_t stop_bit, uint32_t data) {
        if (stop_bit < start_bit) {
            return 0;
        }
        uint8_t bit_len = stop_bit - start_bit + 1;
        return (data >> start_bit) & ((1U << bit_len) - 1);
    }

    /**
     * @brief Check if a value is zero using bitwise AND
     * @param value Value to check
     * @return true if value is zero
     */
    static constexpr bool is_zero(uint32_t value) {
        return (value & 0xFFFFFFFF) == 0;
    }
};

/**
 * @brief Base template class for floating-point formats
 * @tparam TotalBits Total bit width of the format
 * @tparam ExponentBits Number of exponent bits
 * @tparam MantissaBits Number of mantissa bits
 * @tparam BiasValue Exponent bias value
 */
template<uint8_t TotalBits, uint8_t ExponentBits, uint8_t MantissaBits, int16_t BiasValue>
class FloatingPointFormat {
public:
    static constexpr uint8_t TOTAL_BITS = TotalBits;
    static constexpr uint8_t EXPONENT_BITS = ExponentBits;
    static constexpr uint8_t MANTISSA_BITS = MantissaBits;
    static constexpr int16_t BIAS = BiasValue;
    static constexpr uint8_t SIGN_BIT = TotalBits - 1;

    // Bit masks
    static constexpr uint32_t SIGN_MASK = 1U << SIGN_BIT;
    static constexpr uint32_t EXPONENT_MASK = ((1U << ExponentBits) - 1) << MantissaBits;
    static constexpr uint32_t MANTISSA_MASK = (1U << MantissaBits) - 1;

protected:
    uint16_t data_;

public:
    /**
     * @brief Default constructor
     */
    constexpr FloatingPointFormat() : data_(0) {}

    /**
     * @brief Constructor from raw data
     * @param data Raw bit representation
     */
    explicit constexpr FloatingPointFormat(uint16_t data) : data_(data) {}

    /**
     * @brief Get raw data
     * @return Raw bit representation
     */
    constexpr uint16_t data() const { return data_; }

    /**
     * @brief Set raw data
     * @param data Raw bit representation
     */
    void set_data(uint16_t data) { data_ = data; }

    /**
     * @brief Extract sign bit
     * @return Sign bit (0 or 1)
     */
    constexpr uint8_t sign() const {
        return BitUtils::extract_bits(SIGN_BIT, SIGN_BIT, data_);
    }

    /**
     * @brief Extract exponent bits
     * @return Exponent value
     */
    constexpr uint8_t exponent() const {
        uint8_t exp_start = MantissaBits;
        uint8_t exp_stop = MantissaBits + ExponentBits - 1;
        return BitUtils::extract_bits(exp_start, exp_stop, data_);
    }

    /**
     * @brief Extract mantissa bits
     * @return Mantissa value
     */
    constexpr uint16_t mantissa() const {
        return BitUtils::extract_bits(0, MantissaBits - 1, data_);
    }

    /**
     * @brief Check if exponent is zero using bitwise AND
     * @return true if exponent is zero
     */
    constexpr bool is_exponent_zero() const {
        return (exponent() & ((1U << ExponentBits) - 1)) == 0;
    }

    /**
     * @brief Get hidden bit value (1 for normalized, 0 for denormalized)
     * @return Hidden bit value
     */
    constexpr uint8_t hidden_bit() const {
        return is_exponent_zero() ? 0 : 1;
    }

    /**
     * @brief Get regularized exponent for alignment calculations
     * Following the specification: if E==0, reg_exp=1; else reg_exp=E
     * @return Regularized exponent
     */
    constexpr uint8_t regularized_exponent() const {
        uint8_t exp = exponent();
        if (exp == 0) {
            return 1;  // Force to 1 for denormalized numbers
        }
        return exp;
    }

    /**
     * @brief Check if this is a denormalized number
     * @return true if denormalized (exponent == 0)
     */
    constexpr bool is_denormalized() const {
        return is_exponent_zero();
    }

    /**
     * @brief Get bias value for this format
     * @return Bias value
     */
    static constexpr int16_t bias() { return BIAS; }
};

// Specific floating-point format implementations

/**
 * @brief FP16 (Half Precision) floating-point format
 * 16 bits total: 1 sign + 5 exponent + 10 mantissa, bias = 15
 */
class FP16 : public FloatingPointFormat<16, 5, 10, 15> {
public:
    using Base = FloatingPointFormat<16, 5, 10, 15>;

    /**
     * @brief Default constructor
     */
    constexpr FP16() : Base() {}

    /**
     * @brief Constructor from raw data
     * @param data Raw 16-bit representation
     */
    explicit constexpr FP16(uint16_t data) : Base(data) {}

    /**
     * @brief Extract exponent bits using FP16-specific bit positions
     * @return Exponent value (bits 14:10)
     */
    constexpr uint8_t exponent() const {
        return BitUtils::extract_bits(10, 14, data_);
    }

    /**
     * @brief Extract mantissa bits using FP16-specific bit positions
     * @return Mantissa value (bits 9:0)
     */
    constexpr uint16_t mantissa() const {
        return BitUtils::extract_bits(0, 9, data_);
    }

    /**
     * @brief Extract sign bit using FP16-specific bit position
     * @return Sign bit (bit 15)
     */
    constexpr uint8_t sign() const {
        return BitUtils::extract_bits(15, 15, data_);
    }

    /**
     * @brief Check if exponent is zero using bitwise AND
     * @return true if exponent is zero
     */
    constexpr bool is_exponent_zero() const {
        return (exponent() & 0x1F) == 0;  // 5-bit mask
    }

    /**
     * @brief Get format name for debugging
     * @return Format name string
     */
    static constexpr const char* format_name() { return "FP16"; }
};

/**
 * @brief BF16 (Brain Float 16) floating-point format
 * 16 bits total: 1 sign + 8 exponent + 7 mantissa, bias = 127
 */
class BF16 : public FloatingPointFormat<16, 8, 7, 127> {
public:
    using Base = FloatingPointFormat<16, 8, 7, 127>;

    /**
     * @brief Default constructor
     */
    constexpr BF16() : Base() {}

    /**
     * @brief Constructor from raw data
     * @param data Raw 16-bit representation
     */
    explicit constexpr BF16(uint16_t data) : Base(data) {}

    /**
     * @brief Extract exponent bits using BF16-specific bit positions
     * @return Exponent value (bits 14:7)
     */
    constexpr uint8_t exponent() const {
        return BitUtils::extract_bits(7, 14, data_);
    }

    /**
     * @brief Extract mantissa bits using BF16-specific bit positions
     * @return Mantissa value (bits 6:0)
     */
    constexpr uint16_t mantissa() const {
        return BitUtils::extract_bits(0, 6, data_);
    }

    /**
     * @brief Extract sign bit using BF16-specific bit position
     * @return Sign bit (bit 15)
     */
    constexpr uint8_t sign() const {
        return BitUtils::extract_bits(15, 15, data_);
    }

    /**
     * @brief Check if exponent is zero using bitwise AND
     * @return true if exponent is zero
     */
    constexpr bool is_exponent_zero() const {
        return (exponent() & 0xFF) == 0;  // 8-bit mask
    }

    /**
     * @brief Get format name for debugging
     * @return Format name string
     */
    static constexpr const char* format_name() { return "BF16"; }
};

/**
 * @brief FP8E4M3 (4-bit exponent, 3-bit mantissa) floating-point format
 * 8 bits total: 1 sign + 4 exponent + 3 mantissa, bias = 7
 * Data is stored in lower 8 bits of uint16_t
 */
class FP8E4 : public FloatingPointFormat<8, 4, 3, 7> {
public:
    using Base = FloatingPointFormat<8, 4, 3, 7>;

    /**
     * @brief Default constructor
     */
    constexpr FP8E4() : Base() {}

    /**
     * @brief Constructor from raw data
     * @param data Raw 8-bit representation (stored in lower 8 bits of uint16_t)
     */
    explicit constexpr FP8E4(uint16_t data) : Base(data) {}

    /**
     * @brief Extract exponent bits using FP8E4-specific bit positions
     * @return Exponent value (bits 6:3)
     */
    constexpr uint8_t exponent() const {
        return BitUtils::extract_bits(3, 6, data_);
    }

    /**
     * @brief Extract mantissa bits using FP8E4-specific bit positions
     * @return Mantissa value (bits 2:0)
     */
    constexpr uint16_t mantissa() const {
        return BitUtils::extract_bits(0, 2, data_);
    }

    /**
     * @brief Extract sign bit using FP8E4-specific bit position
     * @return Sign bit (bit 7)
     */
    constexpr uint8_t sign() const {
        return BitUtils::extract_bits(7, 7, data_);
    }

    /**
     * @brief Check if exponent is zero using bitwise AND
     * @return true if exponent is zero
     */
    constexpr bool is_exponent_zero() const {
        return (exponent() & 0x0F) == 0;  // 4-bit mask
    }

    /**
     * @brief Get format name for debugging
     * @return Format name string
     */
    static constexpr const char* format_name() { return "FP8E4"; }
};

/**
 * @brief FP8E5M2 (5-bit exponent, 2-bit mantissa) floating-point format
 * 8 bits total: 1 sign + 5 exponent + 2 mantissa, bias = 15
 * Data is stored in lower 8 bits of uint16_t
 */
class FP8E5 : public FloatingPointFormat<8, 5, 2, 15> {
public:
    using Base = FloatingPointFormat<8, 5, 2, 15>;

    /**
     * @brief Default constructor
     */
    constexpr FP8E5() : Base() {}

    /**
     * @brief Constructor from raw data
     * @param data Raw 8-bit representation (stored in lower 8 bits of uint16_t)
     */
    explicit constexpr FP8E5(uint16_t data) : Base(data) {}

    /**
     * @brief Extract exponent bits using FP8E5-specific bit positions
     * @return Exponent value (bits 6:2)
     */
    constexpr uint8_t exponent() const {
        return BitUtils::extract_bits(2, 6, data_);
    }

    /**
     * @brief Extract mantissa bits using FP8E5-specific bit positions
     * @return Mantissa value (bits 1:0)
     */
    constexpr uint16_t mantissa() const {
        return BitUtils::extract_bits(0, 1, data_);
    }

    /**
     * @brief Extract sign bit using FP8E5-specific bit position
     * @return Sign bit (bit 7)
     */
    constexpr uint8_t sign() const {
        return BitUtils::extract_bits(7, 7, data_);
    }

    /**
     * @brief Check if exponent is zero using bitwise AND
     * @return true if exponent is zero
     */
    constexpr bool is_exponent_zero() const {
        return (exponent() & 0x1F) == 0;  // 5-bit mask
    }

    /**
     * @brief Get format name for debugging
     * @return Format name string
     */
    static constexpr const char* format_name() { return "FP8E5"; }
};

/**
 * @brief Main pre-alignment function template declaration
 * @tparam FloatType Floating-point format type (FP16, BF16, FP8E4, FP8E5)
 * @param input_data Array of 32 input values in uint16_t format
 * @param output_data Array of 33 output values (32 aligned mantissas + 1 public exponent)
 */
template<typename FloatType>
void float_prealign(const uint16_t* input_data, uint16_t* output_data);

} // namespace fp_prealign

// C-compatible function declarations
#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief C wrapper for floating-point data alignment (enhanced version)
 * @param data_hex Input array of 32 uint16_t values
 * @param data_type Data type enum (FP16, BF16, FP8E4, FP8E5)
 * @param data_algn Output array of 33 uint16_t values (32 aligned + 1 public exponent)
 */
void float_data_align_cpp(uint16_t *data_hex, uint8_t data_type, uint16_t *data_algn);

/**
 * @brief Enhanced FP16/BF16 alignment function (C++ implementation)
 * @param data_hex Input array of 32 uint16_t values
 * @param data_type Data type enum (FP16 or BF16)
 * @param data_algn Output array of 33 uint16_t values
 */
void data_align_f16b_cpp(uint16_t *data_hex, uint8_t data_type, uint16_t *data_algn);

/**
 * @brief Enhanced FP8 alignment function (C++ implementation)
 * @param data_hex Input array of 32 uint16_t values
 * @param data_type Data type enum (FP8E4 or FP8E5)
 * @param data_algn Output array of 33 uint16_t values
 */
void data_align_f8b_cpp(uint16_t *data_hex, uint8_t data_type, uint16_t *data_algn);

#ifdef __cplusplus
}
#endif

#endif // FP_PREALIGN_HPP