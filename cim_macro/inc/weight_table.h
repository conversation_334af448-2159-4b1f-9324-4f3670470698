#pragma once
#include <stdint.h>
// FP16
static uint16_t wt_mhex_fp16[16][32] = {
          {0x4889, 0x2abf, 0x4309, 0xda60, 0xcc10, 0x351f, 0x552f, 0x3a01, 0xdf24, 0x284e, 0xd556, 0x66b7, 0xa8c7, 0x66e2, 0xdaed, 0xba8f, 0x62e5, 0xc57a, 0x586e, 0x4e1a, 0x5f65, 0x508e, 0xaf67, 0x67b4, 0x4038, 0x5399, 0x3bbb, 0xbb09, 0x3c53, 0x65ac, 0x3d0c, 0x41c3},
          {0x2970, 0xbfb4, 0xb9e3, 0xbbf9, 0x466f, 0x3085, 0x4766, 0x6033, 0xc37b, 0xd33e, 0xe0ff, 0x3fc5, 0x4844, 0xdf55, 0xd21b, 0x63e0, 0xd109, 0xe595, 0xdea5, 0xe336, 0xc961, 0x4958, 0x35b7, 0x2b8d, 0x445c, 0x4a4d, 0xcbd7, 0x5a8b, 0x49a5, 0x2a3e, 0x4aaa, 0xda3d},
          {0x5292, 0xa8b2, 0xdb6c, 0x637b, 0xb6ff, 0x4ea0, 0x3928, 0xb022, 0xe1f2, 0xd51d, 0x5eef, 0x43d1, 0x6676, 0xbea4, 0x5f64, 0xce82, 0xafc1, 0xad2c, 0x2b1f, 0xce17, 0xe40c, 0xc8ca, 0xbaf0, 0xbd47, 0x3d70, 0x3c08, 0x4715, 0x5a17, 0x62b4, 0x5962, 0xe0ba, 0x576d},
          {0x3169, 0x3279, 0x39b5, 0xd901, 0xe5a7, 0x2fa1, 0x28a2, 0xd0f9, 0x612b, 0xd067, 0xd7c5, 0xd725, 0xc594, 0xb7fa, 0x4e0c, 0xdccb, 0x49b7, 0x5f8d, 0xb6b7, 0x5231, 0xaf45, 0xc9cd, 0x651a, 0x33d3, 0x3502, 0x2d30, 0xd610, 0x3968, 0xcac2, 0x5d71, 0xd5c1, 0xb255},
          {0xadee, 0x48bc, 0xd13f, 0x3348, 0xd5bb, 0x2d7c, 0x37c0, 0xb2c6, 0xd936, 0xb66e, 0x4466, 0x3fbc, 0x4974, 0x3492, 0x419f, 0xdc4a, 0x4991, 0xd079, 0x4935, 0x4afb, 0xde72, 0xe25f, 0x659c, 0xe2ea, 0xd7bc, 0xaee1, 0x378e, 0xc69d, 0xbfba, 0x4633, 0xd1c8, 0x4223},
          {0x5fc9, 0x34e5, 0xe60c, 0xe64c, 0xdcf8, 0x628d, 0x5f69, 0x44e5, 0xd52a, 0x3a32, 0x5d8f, 0xd17f, 0x57bf, 0x5d4b, 0x66bd, 0x41fe, 0xa9ee, 0x63d0, 0x53a0, 0x28c4, 0x3d05, 0xc57f, 0x3fbe, 0xbba7, 0x511e, 0x4378, 0x2f47, 0x3221, 0x457b, 0xbf3d, 0xc2e4, 0x2bcd},
          {0x57cb, 0x46cf, 0x33f8, 0xd15f, 0x3249, 0xe4f6, 0x3d44, 0x33d6, 0xbe28, 0x5c13, 0xd8d4, 0x3cc6, 0xa9b3, 0x2d59, 0x35bd, 0xab28, 0xd68c, 0xb642, 0xe3c2, 0xcb46, 0x2c6b, 0xdba5, 0x5f83, 0xb143, 0x39e8, 0x3df0, 0x3702, 0x501e, 0xc1fc, 0x5096, 0x339b, 0x5967},
          {0xdf47, 0xd4f4, 0xdb65, 0xd759, 0x679a, 0x37d1, 0xc005, 0xe7ee, 0xad0f, 0xcb2a, 0xb17b, 0xe627, 0xcb22, 0x35eb, 0x45c1, 0xcd23, 0xc9c6, 0xdd54, 0xdaf5, 0xaf6a, 0xc9dc, 0x60cc, 0x2ac6, 0xb655, 0xced6, 0x4640, 0x6720, 0x2b57, 0xb624, 0x3485, 0xdf1e, 0xabc5},
          {0x66dc, 0x63d1, 0x6314, 0x5411, 0xe313, 0x3bbf, 0xcf86, 0xdf3f, 0x6690, 0x431e, 0xabee, 0x5fc8, 0xc2e4, 0x5cda, 0x3f22, 0x59a5, 0xb9fd, 0x5b5e, 0x5fd7, 0xb50c, 0xcc6a, 0x4094, 0xadcb, 0x5b3b, 0xd820, 0xc386, 0x5143, 0x542d, 0x35b8, 0xb1aa, 0x641a, 0x36bd},
          {0xe61b, 0x4d89, 0x42ad, 0x4578, 0x487d, 0x570b, 0xa971, 0xdb68, 0xb2b4, 0x61f8, 0xb466, 0xe15c, 0x4191, 0x5793, 0xd17e, 0xe165, 0xbc57, 0x468f, 0xcdc4, 0x5988, 0x61c9, 0xb1d8, 0x53ba, 0x5966, 0x3f5c, 0xb568, 0xc9d0, 0x67d3, 0x4da2, 0xe396, 0xd86c, 0xc83c},
          {0xada9, 0xc6ca, 0x67ee, 0xd5af, 0x6642, 0x3b72, 0xb3b8, 0xbeca, 0xe036, 0xb40c, 0xd568, 0x4a98, 0xd78d, 0x5781, 0xcc74, 0x47ff, 0xd277, 0xda03, 0x4b95, 0x60c3, 0x43dc, 0xd7f6, 0x3cc7, 0x47f8, 0xd41d, 0x5ddb, 0x2ba2, 0x2c8c, 0xb8e1, 0xcc7f, 0x62e4, 0x52c1},
          {0x33fc, 0x3d3b, 0x676f, 0x5bbb, 0x57fd, 0x325f, 0x2a23, 0x63dd, 0x619c, 0x672c, 0xd385, 0x4fef, 0xdd25, 0xb137, 0xbd71, 0xd6d9, 0xe499, 0x5ffb, 0xdc09, 0x4691, 0xbe9f, 0xbcbb, 0x4d9e, 0x647c, 0xd69a, 0x33b7, 0xbd53, 0xbd22, 0xcc87, 0xd548, 0xa8a9, 0xd7dc},
          {0x3723, 0xceab, 0x5d8e, 0xb2c6, 0xd234, 0xbabc, 0xc0d4, 0x38c9, 0xe6e7, 0xc3a8, 0x2c39, 0xbf4e, 0x619b, 0x366e, 0x4ec9, 0xc67c, 0x32c2, 0xd8e4, 0xad1c, 0xb698, 0x5009, 0x2e2b, 0xe305, 0x48cf, 0xc9fe, 0xc748, 0xe2da, 0x4229, 0xd40a, 0xb430, 0xbc17, 0x56a1},
          {0x2c8b, 0xdb01, 0x5c26, 0x6337, 0x6280, 0x29d5, 0x4e91, 0xc7e3, 0xcb96, 0x323f, 0x3cc3, 0x2c75, 0xe126, 0xd8ae, 0xc0c3, 0x2bb0, 0xd95b, 0x3366, 0xdb85, 0x39a3, 0xb8a8, 0x4c17, 0xd109, 0x426a, 0x35d7, 0xe3d7, 0x4998, 0xb105, 0xdd3a, 0xe5c9, 0xdef5, 0x381e},
          {0x491a, 0x6423, 0xe090, 0x4e6c, 0x2ae9, 0xbd90, 0xbc20, 0xe7c9, 0xd2fe, 0x5f4a, 0xe465, 0xde1c, 0x354e, 0xc38f, 0xc83a, 0x364e, 0x40df, 0xd59f, 0x4578, 0xd9d9, 0xb00b, 0x40b2, 0xacc0, 0x3815, 0xdcfb, 0x29d6, 0x2aef, 0x42a3, 0x6155, 0x2fb1, 0x5ca6, 0x3682},
          {0xd659, 0x4c55, 0xb960, 0x3355, 0xc4b1, 0xd530, 0xe58b, 0x5d4e, 0xbadf, 0xbe23, 0x6615, 0x6153, 0x6668, 0x4d76, 0x4f47, 0xe211, 0x30b6, 0xace8, 0xe7f6, 0xbead, 0x584d, 0x400b, 0x4c77, 0x58ae, 0xe5e3, 0xb566, 0xe662, 0xcfc1, 0xdaab, 0x3e53, 0x63a4, 0xda11}
};

static uint16_t wt_algn_fp16[16][33] = {
          {0x0091, 0x0000, 0x0038, 0xf340, 0xfefc, 0x0005, 0x052f, 0x000c, 0xe370, 0x0000, 0xfaaa, 0x6b70, 0x0000, 0x6e20, 0xf226, 0xfff3, 0x3728, 0xffa9, 0x08dc, 0x0186, 0x1d94, 0x0247, 0xffff, 0x7b40, 0x0021, 0x03cc, 0x000f, 0xfff2, 0x0011, 0x5ac0, 0x0014, 0x002e, 0x000a},
          {0x0000, 0xffe2, 0xfff5, 0xfff1, 0x0066, 0x0002, 0x0076, 0x2198, 0xffc5, 0xfc61, 0xd808, 0x001f, 0x0088, 0xe2ac, 0xfcf3, 0x3f00, 0xfd7c, 0xa6b0, 0xe56c, 0xc650, 0xff54, 0x00ab, 0x0005, 0x0000, 0x0045, 0x00c9, 0xff06, 0x0d16, 0x00b4, 0x0000, 0x00d5, 0xf386, 0x000a},
          {0x0349, 0x0000, 0xf128, 0x3bd8, 0xfffa, 0x01a8, 0x000a, 0xfffe, 0xd070, 0xfae3, 0x1bbc, 0x003e, 0x6760, 0xffe6, 0x1d90, 0xfe60, 0xffff, 0xffff, 0x0000, 0xfe7b, 0xbf40, 0xff67, 0xfff3, 0xffeb, 0x0015, 0x0010, 0x0071, 0x0c2e, 0x35a0, 0x0ac4, 0xda30, 0x076d, 0x000a},
          {0x0002, 0x0003, 0x000b, 0xf5fe, 0xa590, 0x0001, 0x0000, 0xfd84, 0x2958, 0xfdcd, 0xf83b, 0xf8db, 0xffa7, 0xfff9, 0x0183, 0xecd4, 0x00b6, 0x1e34, 0xfffa, 0x0318, 0xffff, 0xff47, 0x51a0, 0x0003, 0x0005, 0x0001, 0xf9f0, 0x000a, 0xff28, 0x15c4, 0xfa3f, 0xfffd, 0x000a},
          {0xffff, 0x0097, 0xfd61, 0x0003, 0xfa45, 0x0001, 0x0007, 0xfffd, 0xf594, 0xfffa, 0x0046, 0x001e, 0x00ae, 0x0004, 0x002c, 0xeed8, 0x00b2, 0xfdc4, 0x00a6, 0x00df, 0xe638, 0xcd08, 0x59c0, 0xc8b0, 0xf844, 0xffff, 0x0007, 0xff97, 0xffe2, 0x0063, 0xfd1c, 0x0031, 0x000a},
          {0x1f24, 0x0004, 0x9f40, 0x9b40, 0xec20, 0x3468, 0x1da4, 0x004e, 0xfad6, 0x000c, 0x163c, 0xfd41, 0x07bf, 0x152c, 0x6bd0, 0x002f, 0x0000, 0x3e80, 0x03d0, 0x0000, 0x0014, 0xffa9, 0x001e, 0xfff1, 0x028f, 0x003b, 0x0001, 0x0003, 0x0057, 0xffe4, 0xffc9, 0x0000, 0x000a},
          {0x07cb, 0x006c, 0x0003, 0xfd51, 0x0003, 0xb0a0, 0x0015, 0x0003, 0xffe8, 0x104c, 0xf658, 0x0013, 0x0000, 0x0001, 0x0005, 0x0000, 0xf974, 0xfffa, 0xc1f0, 0xff18, 0x0001, 0xf0b6, 0x1e0c, 0xfffe, 0x000b, 0x0017, 0x0007, 0x020f, 0xffd1, 0x024b, 0x0003, 0x0ace, 0x000a},
          {0xe2e4, 0xfb0c, 0xf136, 0xf8a7, 0x79a0, 0x0007, 0xffe0, 0x8120, 0xffff, 0xff1b, 0xfffe, 0x9d90, 0xff1c, 0x0005, 0x005c, 0xfeb8, 0xff48, 0xeab0, 0xf216, 0xffff, 0xff45, 0x2660, 0x0000, 0xfffa, 0xfe4b, 0x0064, 0x7200, 0x0000, 0xfffa, 0x0004, 0xe388, 0x0000, 0x000a},
          {0x6dc0, 0x3e88, 0x38a0, 0x0411, 0xc768, 0x000f, 0xfe1f, 0xe304, 0x6900, 0x0038, 0x0000, 0x1f20, 0xffc9, 0x1368, 0x001c, 0x0b4a, 0xfff5, 0x0ebc, 0x1f5c, 0xfffb, 0xfee6, 0x0024, 0xffff, 0x0e76, 0xf7c0, 0xffc4, 0x02a1, 0x042d, 0x0005, 0xfffe, 0x41a0, 0x0006, 0x000a},
          {0x9e50, 0x0162, 0x0035, 0x0057, 0x008f, 0x070b, 0x0000, 0xf130, 0xfffd, 0x2fc0, 0xfffc, 0xd520, 0x002c, 0x0793, 0xfd41, 0xd4d8, 0xffef, 0x0068, 0xfe8f, 0x0b10, 0x2e48, 0xfffe, 0x03dd, 0x0acc, 0x001d, 0xfffb, 0xff46, 0x7d30, 0x0168, 0xc350, 0xf728, 0xff79, 0x000a},
          {0xffff, 0xff94, 0x7ee0, 0xfa51, 0x6420, 0x000e, 0xfffd, 0xffe5, 0xde50, 0xfffc, 0xfa98, 0x00d3, 0xf873, 0x0781, 0xfee3, 0x007f, 0xfcc5, 0xf3fa, 0x00f2, 0x2618, 0x003e, 0xf80a, 0x0013, 0x007f, 0xfbe3, 0x176c, 0x0000, 0x0001, 0xfff7, 0xfee1, 0x3720, 0x0360, 0x000a},
          {0x0003, 0x0014, 0x76f0, 0x0f76, 0x07fd, 0x0003, 0x0000, 0x3ee8, 0x2ce0, 0x72c0, 0xfc3e, 0x01fb, 0xeb6c, 0xfffe, 0xffeb, 0xf927, 0xb670, 0x1fec, 0xefdc, 0x0069, 0xffe6, 0xffee, 0x0167, 0x47c0, 0xf966, 0x0003, 0xffeb, 0xffec, 0xfedf, 0xfab8, 0x0000, 0xf824, 0x000a},
          {0x0007, 0xfe56, 0x1638, 0xfffd, 0xfce6, 0xfff3, 0xffda, 0x0009, 0x9190, 0xffc3, 0x0001, 0xffe3, 0x2cd8, 0x0006, 0x01b2, 0xff99, 0x0003, 0xf638, 0xffff, 0xfffa, 0x0204, 0x0001, 0xc7d8, 0x0099, 0xff41, 0xff8c, 0xc930, 0x0031, 0xfbf6, 0xfffc, 0xfff0, 0x06a1, 0x000a},
          {0x0001, 0xf1fe, 0x1098, 0x39b8, 0x3400, 0x0000, 0x01a4, 0xff82, 0xff0e, 0x0003, 0x0013, 0x0001, 0xd6d0, 0xf6a4, 0xffda, 0x0000, 0xf54a, 0x0003, 0xf0f6, 0x000b, 0xfff7, 0x0105, 0xfd7c, 0x0033, 0x0005, 0xc148, 0x00b3, 0xfffe, 0xeb18, 0xa370, 0xe42c, 0x0008, 0x000a},
          {0x00a3, 0x4230, 0xdb80, 0x019b, 0x0000, 0xffea, 0xfff0, 0x8370, 0xfc81, 0x1d28, 0xb9b0, 0xe790, 0x0005, 0xffc4, 0xff79, 0x0006, 0x0026, 0xfa61, 0x0057, 0xf44e, 0xfffe, 0x0025, 0xffff, 0x0008, 0xec14, 0x0000, 0x0000, 0x0035, 0x2aa8, 0x0001, 0x1298, 0x0006, 0x000a},
          {0xf9a7, 0x0115, 0xfff6, 0x0003, 0xffb5, 0xfad0, 0xa750, 0x1538, 0xfff3, 0xffe8, 0x6150, 0x2a98, 0x6680, 0x015d, 0x01d1, 0xcf78, 0x0002, 0xffff, 0x80a0, 0xffe6, 0x089a, 0x0020, 0x011d, 0x095c, 0xa1d0, 0xfffb, 0x99e0, 0xfe10, 0xf2aa, 0x0019, 0x3d20, 0xf3de, 0x000a}
};


// BF16
static uint16_t wt_mhex_bf16[16][32] = {
        {0xbe14, 0xbef3, 0xc27a, 0xbe9c, 0xbe6a, 0xc3af, 0xbc8d, 0xbe3c, 0x426e, 0xc1b6, 0x4208, 0x42bd, 0xbe3c, 0x406b, 0x4248, 0x4390, 0x43e6, 0x3f53, 0xc200, 0x40cf, 0x3e04, 0x4208, 0x3ec3, 0xc12a, 0xbe59, 0xbd30, 0x4046, 0x4236, 0xc2be, 0x4305, 0xc3bc, 0xc0ed},
        {0xc1a6, 0x3e71, 0xc1d6, 0xc309, 0xc2be, 0xbd49, 0x43ed, 0x433d, 0x3c46, 0xbc82, 0x4059, 0xbd2f, 0x40b2, 0x409b, 0xc126, 0xbcf0, 0x3d94, 0x3d29, 0xbfb2, 0x3c96, 0x3f55, 0x3e74, 0x402c, 0xc3a6, 0xc10a, 0xbc0a, 0x3df2, 0x3f55, 0xc1d7, 0x40d6, 0xc214, 0x437d},
        {0xc014, 0x3d1d, 0xc364, 0xbc4d, 0x3f98, 0x3e3a, 0xc2f4, 0xbc64, 0xc0a3, 0x3fd9, 0xc3e9, 0x3dd4, 0xbe76, 0x40b4, 0x3fa7, 0xc2d4, 0xc216, 0xbd2d, 0x3d71, 0x3e58, 0x4221, 0xc0b7, 0x42ea, 0x4135, 0xc1f8, 0xc1a9, 0xbe8e, 0xc154, 0x3d50, 0xbf4d, 0xc0be, 0x3de4},
        {0x3fbd, 0xc0ae, 0x3e7b, 0x3fe9, 0xbc75, 0x42fb, 0xbf51, 0x3e84, 0x3fff, 0xbd0b, 0xc20f, 0x4090, 0xc152, 0xc30d, 0x42e7, 0x3fec, 0x3e4a, 0xbe32, 0xbfbe, 0xbe22, 0xbc02, 0x405a, 0xc1e4, 0xbe1f, 0xc2c8, 0xbde2, 0x4248, 0x40af, 0xbf91, 0x3e14, 0xbc89, 0xbc3a},
        {0xbdc8, 0x42a3, 0x3f2e, 0x3cdc, 0xc1b8, 0xc259, 0xc31d, 0xc383, 0x4385, 0x3ffc, 0x424b, 0x3cbd, 0xc031, 0x3c57, 0x40bf, 0x3ee3, 0x4201, 0x3ede, 0xc10a, 0x419b, 0x3c02, 0x4273, 0x3fec, 0x43c1, 0x42d3, 0xc187, 0xc318, 0x415f, 0x3c8d, 0x42bf, 0xbd3f, 0x4374},
        {0x4015, 0x3d2c, 0x40e6, 0xbf52, 0xbef3, 0x3c1e, 0x40e7, 0xc133, 0x41be, 0xbc0b, 0xc34b, 0xbdb4, 0x3d84, 0x3d43, 0x3e94, 0xbec6, 0xc240, 0xc0df, 0xc302, 0x4368, 0xc350, 0xc081, 0x3f73, 0x431d, 0xbd66, 0x3dc6, 0xc2eb, 0xc3ba, 0x3f8a, 0x3c3b, 0x3fb9, 0xbf75},
        {0xc0d8, 0xc029, 0x3c34, 0x419e, 0xc0ae, 0xc3eb, 0x3f39, 0xbd39, 0xc053, 0xbf1b, 0xbcc2, 0x3d0c, 0x3e0f, 0x401c, 0xbf41, 0x3f75, 0x4034, 0x3e7c, 0x404f, 0x3e8b, 0xbc51, 0x4089, 0xc08f, 0xbd42, 0x3d63, 0xbd63, 0xbe0d, 0xbe57, 0x415e, 0x40f9, 0x3fc1, 0x3e81},
        {0xc358, 0xbe8e, 0x43e4, 0xbe8f, 0xc11c, 0x42d0, 0x3e32, 0xbc7d, 0x3c04, 0xbf57, 0x3f89, 0xc269, 0xbcbf, 0x43ba, 0xbdbf, 0xbde5, 0xc038, 0x3e2c, 0x40c7, 0xc0d6, 0xc088, 0x3f25, 0xc383, 0x43fd, 0x3c66, 0xbfa8, 0xc057, 0x3f39, 0x43e8, 0xbc56, 0xc3f9, 0x4133},
        {0x41a5, 0xc0e6, 0xbc27, 0x3d89, 0x4253, 0x4328, 0xbfc1, 0x3fa1, 0x3c75, 0x405c, 0x3c44, 0x3c9c, 0xbd90, 0xc084, 0xc0ca, 0xc2b3, 0xbd9a, 0x412a, 0x3c6b, 0x3f8a, 0x3d06, 0xbc1b, 0x3f04, 0x3eb4, 0x3e77, 0xbe0d, 0xbff2, 0xbf29, 0x3da5, 0xbe46, 0x3e93, 0xc162},
        {0x4095, 0x4154, 0x3d97, 0x3c05, 0xc061, 0xc37e, 0xbf77, 0xbd21, 0x3c61, 0xbf82, 0x4345, 0x4364, 0xc22b, 0x3fa6, 0x3e5a, 0x406c, 0x41f7, 0x3cca, 0x42d8, 0xc136, 0xbca0, 0x3e89, 0x43ef, 0xc310, 0xbd01, 0xbe32, 0x3f62, 0xbf40, 0xc3a8, 0xc238, 0xbd77, 0x3f9a},
        {0xbf72, 0xc2ff, 0xbefe, 0x42bc, 0x4152, 0xbf18, 0xbf7a, 0xc0d6, 0x3f68, 0xc2e0, 0xbf97, 0x40a9, 0x41d7, 0xbcb5, 0xc2ed, 0xc028, 0xbdac, 0x411b, 0xc3e3, 0x4032, 0x3ec3, 0xc067, 0x437b, 0x422a, 0x3c35, 0x404f, 0xbf09, 0xc3a7, 0x3e39, 0xbc7a, 0x4231, 0xc223},
        {0x422e, 0x411f, 0xbe5b, 0xc02c, 0xbe40, 0x42e3, 0x40ff, 0xc0b7, 0xbd78, 0xbec8, 0xbf0b, 0xc1c0, 0xbf69, 0x3fe3, 0xbe12, 0xc1cb, 0x43d1, 0xbc9d, 0xc349, 0x3e85, 0x42b4, 0xbf0c, 0xbea3, 0xc2b7, 0xc28c, 0xbd83, 0x4327, 0x3e86, 0xc0dc, 0xbd7e, 0xc354, 0xbd5e},
        {0xbf15, 0x3f60, 0xbdd0, 0xc17a, 0x3c1b, 0x408b, 0x423c, 0x410a, 0x40d4, 0xc261, 0x43ff, 0xc26a, 0x3f8c, 0x3cf0, 0x3ce5, 0x3e31, 0xc354, 0x3de4, 0xbc21, 0x40da, 0xc112, 0xbf06, 0xc2ab, 0xc266, 0x42d2, 0x400c, 0x3c14, 0x43dd, 0x3ceb, 0xc195, 0xc2e7, 0x3c3b},
        {0xc351, 0xc103, 0xc246, 0x3d2a, 0xbfd9, 0x3cee, 0xc20a, 0x3ff1, 0x3d7c, 0xbf95, 0xbc03, 0x4192, 0xbc55, 0xbc3d, 0xc341, 0x3dee, 0x3e57, 0x41ae, 0xbd71, 0x3f3e, 0xbc5e, 0xc0eb, 0xc33c, 0xc132, 0xbc61, 0x3f20, 0x4203, 0xbe59, 0xbfee, 0xc019, 0x4066, 0xbdeb},
        {0x3e5c, 0xbe99, 0xc137, 0x41d3, 0x3e8b, 0x40c8, 0xc220, 0x3dc3, 0xc25b, 0xc3a7, 0x3fa1, 0x3e72, 0x3d42, 0xbd76, 0xc293, 0x3c19, 0x40f1, 0x4071, 0x4051, 0x3eb2, 0x3c62, 0x3e0f, 0x3cf9, 0x4376, 0xc390, 0xc2e0, 0xc291, 0xc191, 0xbee5, 0x3d11, 0xc0e7, 0xbcd4},
        {0x3fac, 0x3c11, 0x40f2, 0x4293, 0xc223, 0xbc87, 0x3cfc, 0xc2fb, 0x43de, 0x3d42, 0x42bf, 0xc180, 0x4076, 0x42ff, 0xc23c, 0xc302, 0xc198, 0x43e3, 0xc3b7, 0xbe5c, 0xbf35, 0x3f03, 0x3d86, 0xbe9f, 0x424d, 0xbeb9, 0x4315, 0xbe8d, 0xc203, 0x4150, 0x43d8, 0x3fd5}
};

static uint16_t wt_algn_bf16[16][33] = {
        {0xfff7, 0xffe2, 0xf060, 0xffed, 0xfff2, 0xa880, 0xffff, 0xfff5, 0x0ee0, 0xfa50, 0x0880, 0x17a0, 0xfff5, 0x00eb, 0x0c80, 0x4800, 0x7300, 0x0034, 0xf800, 0x019e, 0x0008, 0x0880, 0x0018, 0xfd58, 0xfff3, 0xfffe, 0x00c6, 0x0b60, 0xe840, 0x2140, 0xa200, 0xfe26, 0x0008},
        {0xfad0, 0x000f, 0xf950, 0xddc0, 0xe840, 0xfffd, 0x7680, 0x2f40, 0x0000, 0xffff, 0x00d9, 0xfffe, 0x0164, 0x0136, 0xfd68, 0xffff, 0x0004, 0x0002, 0xffa7, 0x0001, 0x0035, 0x000f, 0x00ac, 0xad00, 0xfdd8, 0x0000, 0x0007, 0x0035, 0xf948, 0x01ac, 0xf6c0, 0x3f40, 0x0008},
        {0xff6c, 0x0002, 0xc700, 0x0000, 0x004c, 0x000b, 0xe180, 0x0000, 0xfeba, 0x006c, 0x8b80, 0x0006, 0xfff1, 0x0168, 0x0053, 0xe580, 0xf6a0, 0xfffe, 0x0003, 0x000d, 0x0a10, 0xfe92, 0x1d40, 0x02d4, 0xf840, 0xfab8, 0xffef, 0xfcb0, 0x0003, 0xffcd, 0xfe84, 0x0007, 0x0008},
        {0x00bd, 0xfd48, 0x001f, 0x00e9, 0xffff, 0x3ec0, 0xff98, 0x0021, 0x00ff, 0xfffc, 0xee20, 0x0240, 0xf970, 0xb980, 0x39c0, 0x00ec, 0x0019, 0xffea, 0xff42, 0xffec, 0xffff, 0x01b4, 0xf1c0, 0xffed, 0xce00, 0xfff2, 0x1900, 0x02bc, 0xff6f, 0x0012, 0xfffe, 0xffff, 0x0007},
        {0xfffa, 0x1460, 0x002b, 0x0001, 0xfa40, 0xf270, 0xd8c0, 0xbe80, 0x4280, 0x007e, 0x0cb0, 0x0001, 0xff4f, 0x0000, 0x017e, 0x001c, 0x0810, 0x001b, 0xfdd8, 0x04d8, 0x0000, 0x0f30, 0x0076, 0x6080, 0x1a60, 0xfbc8, 0xda00, 0x037c, 0x0001, 0x17e0, 0xfffe, 0x3d00, 0x0008},
        {0x0095, 0x0002, 0x01cc, 0xffcc, 0xffe2, 0x0000, 0x01ce, 0xfd34, 0x05f0, 0x0000, 0xcd40, 0xfffb, 0x0004, 0x0003, 0x0012, 0xffe8, 0xf400, 0xfe42, 0xdf80, 0x3a00, 0xcc00, 0xfefe, 0x003c, 0x2740, 0xfffd, 0x0006, 0xe2a0, 0xa300, 0x0045, 0x0000, 0x005c, 0xffc3, 0x0008},
        {0xfe50, 0xff57, 0x0000, 0x04f0, 0xfea4, 0x8a80, 0x002e, 0xfffe, 0xff2d, 0xffda, 0xffff, 0x0002, 0x0008, 0x009c, 0xffd0, 0x003d, 0x00b4, 0x000f, 0x00cf, 0x0011, 0x0000, 0x0112, 0xfee2, 0xfffd, 0x0003, 0xfffd, 0xfff8, 0xfff3, 0x0378, 0x01f2, 0x0060, 0x0010, 0x0008},
        {0xca00, 0xffef, 0x7200, 0xffef, 0xfd90, 0x1a00, 0x000b, 0x0000, 0x0000, 0xffcb, 0x0044, 0xf170, 0xffff, 0x5d00, 0xfffb, 0xfff9, 0xff48, 0x000a, 0x018e, 0xfe54, 0xfef0, 0x0029, 0xbe80, 0x7e80, 0x0000, 0xffac, 0xff29, 0x002e, 0x7400, 0x0000, 0x8380, 0x02cc, 0x0008},
        {0x0a50, 0xfc68, 0xffff, 0x0008, 0x1a60, 0x5400, 0xff3f, 0x00a1, 0x0001, 0x01b8, 0x0001, 0x0002, 0xfff7, 0xfdf0, 0xfcd8, 0xd340, 0xfff7, 0x0550, 0x0001, 0x008a, 0x0004, 0xffff, 0x0042, 0x002d, 0x001e, 0xffef, 0xff0e, 0xffac, 0x000a, 0xffe8, 0x0024, 0xf8f0, 0x0007},
        {0x012a, 0x0350, 0x0004, 0x0000, 0xff1f, 0xc080, 0xffc3, 0xfffe, 0x0000, 0xffbf, 0x3140, 0x3900, 0xf550, 0x0053, 0x000d, 0x00ec, 0x07b8, 0x0001, 0x1b00, 0xfd28, 0xffff, 0x0011, 0x7780, 0xdc00, 0xfffe, 0xfff5, 0x0038, 0xffd0, 0xac00, 0xf480, 0xfffd, 0x004d, 0x0008},
        {0xffc4, 0xe020, 0xffe1, 0x1780, 0x0348, 0xffda, 0xffc2, 0xfe54, 0x003a, 0xe400, 0xffb5, 0x0152, 0x06b8, 0xffff, 0xe260, 0xff58, 0xfffb, 0x026c, 0x8e80, 0x00b2, 0x0018, 0xff19, 0x3ec0, 0x0aa0, 0x0000, 0x00cf, 0xffde, 0xac80, 0x000b, 0x0000, 0x0b10, 0xf5d0, 0x0008},
        {0x0ae0, 0x027c, 0xfff3, 0xff54, 0xfff4, 0x1c60, 0x01fe, 0xfe92, 0xfffd, 0xffe7, 0xffde, 0xfa00, 0xffc6, 0x0071, 0xfff7, 0xf9a8, 0x6880, 0xffff, 0xcdc0, 0x0010, 0x1680, 0xffdd, 0xffec, 0xe920, 0xee80, 0xfffc, 0x29c0, 0x0010, 0xfe48, 0xfffd, 0xcb00, 0xfffd, 0x0008},
        {0xffdb, 0x0038, 0xfffa, 0xfc18, 0x0000, 0x0116, 0x0bc0, 0x0228, 0x01a8, 0xf1f0, 0x7f80, 0xf160, 0x0046, 0x0001, 0x0001, 0x000b, 0xcb00, 0x0007, 0x0000, 0x01b4, 0xfdb8, 0xffdf, 0xeaa0, 0xf1a0, 0x1a40, 0x008c, 0x0000, 0x6e80, 0x0001, 0xfb58, 0xe320, 0x0000, 0x0008},
        {0x9780, 0xfbe8, 0xe740, 0x0005, 0xff27, 0x0003, 0xeec0, 0x00f1, 0x0007, 0xff6b, 0xffff, 0x0920, 0xffff, 0xffff, 0x9f80, 0x000e, 0x001a, 0x0ae0, 0xfff9, 0x005f, 0xffff, 0xfc54, 0xa200, 0xfa70, 0xffff, 0x0050, 0x1060, 0xffe5, 0xff12, 0xfece, 0x01cc, 0xfff2, 0x0007},
        {0x000d, 0xffed, 0xfd24, 0x0698, 0x0011, 0x0190, 0xf600, 0x0006, 0xf250, 0xac80, 0x0050, 0x000f, 0x0003, 0xfffd, 0xeda0, 0x0000, 0x01e2, 0x00f1, 0x00d1, 0x0016, 0x0000, 0x0008, 0x0001, 0x3d80, 0xb800, 0xe400, 0xede0, 0xfb78, 0xffe4, 0x0002, 0xfe32, 0xffff, 0x0008},
        {0x0056, 0x0000, 0x01e4, 0x1260, 0xf5d0, 0xffff, 0x0001, 0xe0a0, 0x6f00, 0x0003, 0x17e0, 0xfc00, 0x00f6, 0x1fe0, 0xf440, 0xdf80, 0xfb40, 0x7180, 0xa480, 0xfff3, 0xffd3, 0x0020, 0x0004, 0xffed, 0x0cd0, 0xffe9, 0x2540, 0xffef, 0xf7d0, 0x0340, 0x6c00, 0x006a, 0x0008}
};


// BBF16
static uint16_t wt_mhex_bbf16[32][32] = {
          {0xc095, 0xbf07, 0xbfe9, 0x3d7d, 0xbc16, 0x40bf, 0x42ec, 0xc004, 0x4397, 0x434c, 0x4334, 0xbf8f, 0x3c4f, 0x4334, 0x41ab, 0x3d01, 0xc1ea, 0x42ca, 0x4288, 0xc018, 0x3fc6, 0xc0a4, 0x3f16, 0x4241, 0x3e10, 0xc129, 0xc2d7, 0xbe67, 0x40cd, 0xc101, 0x3cfb, 0xbd86},
          {0xc022, 0x3e0c, 0xc38e, 0xbf3d, 0xbc33, 0x3ca8, 0xc325, 0x41cb, 0x3f07, 0xbde3, 0x42bd, 0xbcc8, 0xbf8b, 0xbf86, 0xbd98, 0xbd02, 0x415e, 0xbfc3, 0x421b, 0x3fab, 0xc3bc, 0x3f36, 0xbe5a, 0x4001, 0x3cc5, 0x416c, 0xc2af, 0x3e68, 0x4137, 0x3d6e, 0xc120, 0xbeb2},
          {0xbf9f, 0xbe0d, 0x3f63, 0x3c14, 0x3fbd, 0x3c25, 0xbd78, 0x42bf, 0xbdc6, 0x403b, 0x3db7, 0x4358, 0x3d5f, 0x3f8f, 0x4275, 0xbcc4, 0xc1c1, 0x3cbe, 0x3da8, 0x41a4, 0xc3aa, 0x3d1c, 0x3c2d, 0xbd55, 0xc302, 0x3e25, 0xbd55, 0xbdca, 0xc235, 0x4310, 0xbc68, 0x3e8f},
          {0x410b, 0x3e83, 0xc2a2, 0x401d, 0x40c8, 0xc322, 0xbe41, 0x425f, 0xbf2e, 0x40b4, 0x43f8, 0x412d, 0x3f2a, 0x3cbd, 0xbfda, 0xbf75, 0x4164, 0x42eb, 0xc024, 0x43fc, 0xc06a, 0xc35d, 0xc3f2, 0xbd10, 0x4107, 0xc2aa, 0x43a6, 0xbd5f, 0x4063, 0xbd51, 0x3e9c, 0xc19f},
          {0x3f74, 0x40c1, 0xbeec, 0x3dd0, 0xc055, 0xbdbf, 0x3f70, 0xbd99, 0x3c6b, 0x415b, 0x3d78, 0x41a0, 0x3d2f, 0x3c84, 0x4304, 0x43b9, 0x432a, 0x3d4b, 0xbe0b, 0x3d58, 0xbdbd, 0x40c0, 0xc3b5, 0xc072, 0x4350, 0xc08c, 0x4269, 0x42ee, 0xbed0, 0x4194, 0xbc09, 0x3ce8},
          {0xc359, 0xc210, 0x4342, 0xbf6c, 0xbe37, 0x42d6, 0xbd1f, 0xbd93, 0x41b1, 0xbc6a, 0x3ece, 0x3ca4, 0x4192, 0xbdce, 0x3f37, 0x3f09, 0xc3a5, 0xc1ff, 0x4188, 0x3e9b, 0x3db0, 0xbfdd, 0x4381, 0xbc71, 0x42c6, 0xc3f5, 0xbf86, 0xc222, 0xc2e3, 0x4221, 0x3d1c, 0xc22f},
          {0x3dac, 0xbfce, 0x3f8b, 0xc163, 0xc17b, 0xbe4e, 0x3f0c, 0x40b6, 0xc223, 0xbd05, 0xbd24, 0x42a3, 0xbee6, 0x41e6, 0xc027, 0x4281, 0xbe6e, 0x4016, 0xc16a, 0x3c42, 0x3d26, 0x434b, 0x3e69, 0x3d94, 0x42bd, 0xc382, 0x3fdd, 0xc2b2, 0xbe54, 0x43e5, 0xc248, 0x3fe9},
          {0x3f53, 0xc1a5, 0xc248, 0xc15c, 0xc3cb, 0x43c3, 0xc04e, 0x4370, 0x3d36, 0x3f92, 0x4268, 0xbff6, 0x4067, 0x422b, 0xbf25, 0x4340, 0x3de5, 0x3ff9, 0xc301, 0x429b, 0x4356, 0x43e9, 0xbdf3, 0x42ac, 0xbc7b, 0xc1e2, 0x3c26, 0x3d83, 0x3c21, 0x3d32, 0x3dd5, 0xbfba},
          {0xbc15, 0xc08f, 0xc12f, 0xc36a, 0xbe48, 0x3fef, 0xbef0, 0x4231, 0x3f71, 0xc07a, 0x3f49, 0xbeae, 0x3ed8, 0x4387, 0x3c5b, 0x42af, 0x3c31, 0xbdfb, 0x3dc2, 0x3f21, 0xc2aa, 0xbf3a, 0x3db7, 0x4024, 0x417d, 0xbdc9, 0x40ff, 0x401b, 0x3fcc, 0x40ff, 0x4120, 0x3f6e},
          {0x42b1, 0xbcc7, 0x3ef1, 0x3f0f, 0x4045, 0xbfd1, 0x4170, 0x41b6, 0xc002, 0x4243, 0x3d78, 0x3c00, 0x42b6, 0x433f, 0xbdf5, 0xc164, 0xc1a5, 0x3c91, 0x411b, 0x3f02, 0x43ac, 0xbfe0, 0x3cfc, 0xbdb6, 0xc1b9, 0xc373, 0xbf48, 0xbfcd, 0xc2cf, 0xc2a8, 0x4327, 0x421c},
          {0x3c8c, 0xbf54, 0x3f92, 0xc2c9, 0x3f13, 0xc055, 0x4293, 0xbf51, 0xbd2e, 0x3c52, 0x4177, 0xbc92, 0xbf57, 0xbcda, 0xbf60, 0xbe2a, 0xc232, 0xc3f3, 0x4347, 0x3fd2, 0x42be, 0xc101, 0x42d1, 0xbdad, 0x4374, 0x3c87, 0xc331, 0xc119, 0x40b1, 0x3edd, 0x4140, 0xc0ea},
          {0x3d68, 0xbd29, 0xbee3, 0xc272, 0x43cb, 0x43c2, 0xbc94, 0xbc9e, 0xc1ea, 0xc39a, 0xbde4, 0x407b, 0xbd53, 0xbd70, 0xc0ff, 0xc0fa, 0x4329, 0xbf37, 0xbc82, 0xc33c, 0xbedf, 0x418a, 0xbcf2, 0xbdc8, 0x4050, 0x3e39, 0x3ed0, 0x4383, 0x3e03, 0xc27d, 0xbd9c, 0xc2e5},
          {0x3e68, 0xbe08, 0x42e7, 0x40f9, 0x42a0, 0x3f79, 0x4322, 0xbfb0, 0xbd3f, 0x434a, 0xc2ab, 0xc178, 0x40ca, 0xbe57, 0xc028, 0x3e8a, 0xc3e2, 0x4301, 0x3c54, 0x3e5f, 0x3e69, 0x41ab, 0x42f6, 0xc3f3, 0xbcc0, 0xbfb1, 0x41db, 0x3ebd, 0xbfe7, 0xc1e2, 0x3dd8, 0x3c53},
          {0xbd2a, 0x413d, 0x4124, 0x3e9d, 0xc113, 0x42df, 0xbf35, 0x4006, 0xbf64, 0xc1f8, 0x3c93, 0x430d, 0x3f4d, 0x3f5f, 0xbe79, 0x3f8d, 0x3c2c, 0xbf4b, 0x3d13, 0xbdf4, 0x43b1, 0xbdc6, 0x4306, 0xbe5f, 0x4384, 0x3e60, 0xc215, 0xc035, 0x41db, 0xc3df, 0xbcd0, 0x3db5},
          {0xbda1, 0xc3c0, 0xc264, 0x3c44, 0x43df, 0xc16b, 0xc2cb, 0xbe61, 0xbd03, 0xbdac, 0xc003, 0xc1c7, 0xc39b, 0xc1c2, 0x403d, 0x3ec2, 0x3d0b, 0x3ce7, 0x42e4, 0x434c, 0xc31c, 0x4036, 0x3f61, 0x43a2, 0xc3ff, 0x3eb6, 0xc0c1, 0xc246, 0x3cdd, 0x3cf3, 0xbf87, 0x42cb},
          {0x4316, 0x3f4a, 0x43bc, 0xbc24, 0x3d6c, 0x3f35, 0xbe77, 0x41a7, 0xbd75, 0x4050, 0xc1f1, 0x3ccb, 0x3e97, 0xc1df, 0x42e2, 0xc36f, 0xc38f, 0xc05c, 0xc359, 0x4025, 0xc3e7, 0xc0c0, 0xc2e5, 0x3e7a, 0x3e29, 0x3f63, 0xc2e0, 0x3fa9, 0x3db7, 0x433b, 0xc311, 0xc370},
          {0xbc7d, 0xbe58, 0xbe45, 0xc358, 0xc3dc, 0xbf17, 0xbfb3, 0xbda0, 0x3e67, 0xbf29, 0x40da, 0x3ce2, 0xc0b7, 0xbccd, 0x3e1b, 0xc230, 0x3ff6, 0x3df5, 0x3d20, 0x3f76, 0xbe30, 0xc0ab, 0x425e, 0xbe5c, 0xc25f, 0xbfa8, 0x4388, 0x3fc5, 0xbc78, 0xbf15, 0xc160, 0x4175},
          {0xbf46, 0xc0e0, 0xbf1b, 0xbfce, 0xbe3c, 0xbd8f, 0x4152, 0x3c43, 0x3c30, 0x4007, 0x3e5c, 0xc0aa, 0xbf1f, 0xbdf8, 0x42fb, 0x3e96, 0x3f1e, 0x4149, 0xc14c, 0xbcf3, 0xbd41, 0xbd65, 0xbf7b, 0xbff8, 0xc035, 0x3ed3, 0x3e3b, 0xc161, 0xc321, 0xbcf4, 0x42ea, 0xc24a},
          {0x4169, 0xbe12, 0x3f7b, 0xbf1f, 0x40c1, 0x43c9, 0xc130, 0x3dd1, 0xbcce, 0x4234, 0xc031, 0xc185, 0x3eb5, 0xbefe, 0x40dc, 0xbc00, 0xc0cd, 0xbcac, 0x3e2e, 0x3f64, 0xbfe8, 0x40d7, 0x42ed, 0x414e, 0xc094, 0x4248, 0xbe84, 0xc061, 0x41b9, 0x4259, 0x4180, 0xbd2b},
          {0x4354, 0xbeeb, 0x3e9b, 0x3d09, 0xc3d6, 0x3d65, 0x3c09, 0xc256, 0xc303, 0xc231, 0xc1c9, 0x3d02, 0xbdef, 0x3e1f, 0x4219, 0x3de6, 0x3ef0, 0x3cba, 0xc002, 0xc0cf, 0xc3f5, 0x3ed5, 0x4349, 0x3e26, 0xc068, 0xbdfe, 0xc336, 0xc297, 0x40ae, 0xbce6, 0x3eff, 0x3fd9},
          {0xc1e6, 0x3f75, 0x42b8, 0xbd46, 0x3fa9, 0xbed9, 0xc34f, 0xbe99, 0xc3cf, 0xc3a8, 0xc3ba, 0xbef6, 0xbf81, 0x430e, 0xbf7f, 0x3c91, 0x3d10, 0x4210, 0xc2b9, 0x417e, 0x4117, 0xc26f, 0xc123, 0xbf17, 0x3c1b, 0x4278, 0xc078, 0xbc4c, 0x3fed, 0xc054, 0x43a5, 0x3c09},
          {0xbd48, 0x40ce, 0x3d38, 0x403e, 0xc029, 0xc047, 0x3fac, 0x3cc5, 0x3fef, 0x3d62, 0x3c1f, 0x432a, 0x421a, 0xbf35, 0x3c2a, 0xc183, 0xbd0c, 0xc22b, 0x40ba, 0x3e1a, 0xc2a1, 0x4173, 0xbffc, 0x40b6, 0xbe62, 0x3f1d, 0x3d01, 0xbc94, 0xbc6d, 0xc0b4, 0xbf7e, 0xbe65},
          {0x43ba, 0xc391, 0x3eed, 0x3fde, 0x43e0, 0xbf7b, 0x4063, 0x4237, 0x3e9c, 0x3ebf, 0x403f, 0x3d39, 0xc2db, 0x426d, 0x409c, 0x4161, 0xbf27, 0x40b8, 0xc02c, 0x4219, 0xbc94, 0x3e92, 0xc387, 0xbd9f, 0xbefa, 0x437e, 0x3d18, 0xc22a, 0xbe6c, 0x4082, 0x3d86, 0x3d6c},
          {0xc088, 0x40d9, 0xc365, 0xc2d9, 0x3e03, 0xc3cd, 0x3d18, 0x4054, 0x4033, 0x3ef2, 0xbcc3, 0x3c53, 0xbdd6, 0x41f9, 0xbc98, 0x4160, 0x3ee6, 0x3ebd, 0xc3ce, 0x4372, 0xc3b9, 0xbe9f, 0x3fa2, 0x402c, 0xbc01, 0x42d7, 0xbc0f, 0x418d, 0x3fd8, 0xbe3b, 0xc398, 0xc103},
          {0xc1f9, 0xbd2a, 0xbf42, 0xc151, 0xbd96, 0xbf58, 0xc20c, 0xc2f7, 0xc031, 0x3c4a, 0xc25f, 0x421e, 0xc261, 0xc0c9, 0xbdf4, 0x41f0, 0xc1fd, 0x3fe4, 0x3e10, 0xc234, 0xbc11, 0x41e1, 0x41b9, 0x4356, 0x430c, 0x3ea5, 0x4287, 0xc0b5, 0xc146, 0xbf87, 0x41b8, 0xbd01},
          {0xc14d, 0x3f7d, 0xbcbb, 0x3daf, 0x42b2, 0x43c7, 0xbdd2, 0x42d4, 0xc3f7, 0xc33c, 0xbee7, 0xc305, 0x43cc, 0x40dd, 0xbf6f, 0x3f7d, 0x4316, 0x3c91, 0x40ae, 0x4330, 0xbc3e, 0xbdef, 0x43ac, 0x3d3e, 0xbe8d, 0xc2bc, 0xc14d, 0xbe9e, 0x3f1b, 0xbc31, 0x3ec0, 0xbfb8},
          {0xbf94, 0x4014, 0x41b5, 0xc2c2, 0x3dfc, 0xbd6f, 0x40c5, 0xc1b7, 0x424f, 0x3e45, 0xbd45, 0x40f9, 0xc2be, 0x4122, 0xc062, 0xbc28, 0xc371, 0x40a4, 0x3f50, 0xc138, 0x406e, 0x3dcc, 0x42b4, 0x3d58, 0xbf1d, 0xbed9, 0x43c9, 0x43e8, 0xc39f, 0xc051, 0xc2f7, 0x3f1f},
          {0x3f25, 0x402e, 0xbe72, 0xc07a, 0x3fe0, 0x3d84, 0x42ab, 0x3c68, 0xc07f, 0x4211, 0x3f5f, 0xc123, 0x3c0b, 0x4022, 0x426c, 0xc2a9, 0xc1b2, 0x3e6b, 0xc20c, 0xc286, 0xbf78, 0x42c1, 0xbcb9, 0x3cf3, 0xc009, 0xc0fb, 0x3e23, 0xbcc8, 0xbf7d, 0xbe85, 0xbee6, 0xbc34},
          {0x3f46, 0x3c04, 0xc09a, 0xbc4b, 0x3fc4, 0x3d68, 0xbd2b, 0x4043, 0xc0f7, 0xbcfc, 0xbf87, 0xbfb8, 0xc02d, 0x4309, 0xbe1c, 0xbe6d, 0x4193, 0xbe06, 0x3e42, 0xc162, 0xc0e9, 0x3f35, 0x3c4e, 0xbe3b, 0x43fe, 0x3f9b, 0xbd9c, 0xbfce, 0xbfe3, 0xc090, 0xbdff, 0x3faa},
          {0x3c91, 0xc00b, 0x3f17, 0xbef5, 0xbd55, 0xbe0d, 0xc0da, 0x41a0, 0xbef8, 0x43db, 0xbd59, 0x3df6, 0x3d5b, 0xc0d3, 0xbc6f, 0xc12b, 0x3e99, 0x3de5, 0x3e95, 0xbe0c, 0xbc1a, 0xc227, 0x425f, 0x3c68, 0x43af, 0x3e92, 0x422c, 0xc003, 0x41da, 0xc01f, 0xbe7c, 0x3c7e},
          {0xc337, 0x41fe, 0x417b, 0x3eac, 0xbd86, 0xbe80, 0x3fd7, 0xc0e3, 0xc23c, 0x3f32, 0xc31c, 0x4284, 0x42f9, 0x4170, 0x3f54, 0x3e09, 0x3d02, 0x439f, 0xc1a7, 0x3cc2, 0x3f89, 0xc205, 0x4164, 0x40e7, 0x3ece, 0x3d5d, 0xbe69, 0x3f76, 0xc3ad, 0xbe9f, 0xbd17, 0x415b},
          {0xc149, 0xbd56, 0xc08d, 0xbede, 0x4009, 0xbd3e, 0xbf93, 0xbceb, 0xc274, 0x4218, 0x41d4, 0xbc2d, 0x3e96, 0x41fe, 0xc2e2, 0xc185, 0xc260, 0x3ea9, 0x3e19, 0x3d54, 0xc0dd, 0x3e89, 0x408b, 0x40a4, 0x3de3, 0x3c43, 0xc23e, 0xbd02, 0xbff4, 0x4087, 0x418d, 0xc219}
};


static uint16_t wt_algn_bbf16[32][33] = {
        {0xfe, 0xff, 0xff, 0x00, 0x00, 0x01, 0x1d, 0xff, 0x4b, 0x33, 0x2d, 0xff, 0x00, 0x2d, 0x05, 0x00, 0xf8, 0x19, 0x11, 0xff, 0x00, 0xfe, 0x00, 0x0c, 0x00, 0xfd, 0xe5, 0xff, 0x01, 0xfe, 0x00, 0xff, 0x08},
        {0xff, 0x00, 0xb9, 0xff, 0x00, 0x00, 0xd7, 0x06, 0x00, 0xff, 0x17, 0xff, 0xff, 0xff, 0xff, 0xff, 0x03, 0xff, 0x09, 0x00, 0xa2, 0x00, 0xff, 0x00, 0x00, 0x03, 0xea, 0x00, 0x02, 0x00, 0xfd, 0xff, 0x08},
        {0xff, 0xff, 0x00, 0x00, 0x00, 0x00, 0xff, 0x17, 0xff, 0x00, 0x00, 0x36, 0x00, 0x00, 0x0f, 0xff, 0xfa, 0x00, 0x00, 0x05, 0xab, 0x00, 0x00, 0xff, 0xdf, 0x00, 0xff, 0xff, 0xf4, 0x24, 0x00, 0x00, 0x08},
        {0x02, 0x00, 0xeb, 0x00, 0x01, 0xd7, 0xff, 0x0d, 0xff, 0x01, 0x7c, 0x02, 0x00, 0x00, 0xff, 0xff, 0x03, 0x1d, 0xff, 0x7e, 0xff, 0xc9, 0x87, 0xff, 0x02, 0xea, 0x53, 0xff, 0x00, 0xff, 0x00, 0xfb, 0x08},
        {0x00, 0x01, 0xff, 0x00, 0xff, 0xff, 0x00, 0xff, 0x00, 0x03, 0x00, 0x05, 0x00, 0x00, 0x21, 0x5c, 0x2a, 0x00, 0xff, 0x00, 0xff, 0x01, 0xa6, 0xff, 0x34, 0xfe, 0x0e, 0x1d, 0xff, 0x04, 0x00, 0x00, 0x08},
        {0xca, 0xf7, 0x30, 0xff, 0xff, 0x1a, 0xff, 0xff, 0x05, 0x00, 0x00, 0x00, 0x04, 0xff, 0x00, 0x00, 0xae, 0xf8, 0x04, 0x00, 0x00, 0xff, 0x40, 0x00, 0x18, 0x86, 0xff, 0xf5, 0xe3, 0x0a, 0x00, 0xf5, 0x08},
        {0x00, 0xff, 0x00, 0xfc, 0xfc, 0xff, 0x00, 0x01, 0xf5, 0xff, 0xff, 0x14, 0xff, 0x07, 0xff, 0x10, 0xff, 0x00, 0xfc, 0x00, 0x00, 0x32, 0x00, 0x00, 0x17, 0xbf, 0x00, 0xe9, 0xff, 0x72, 0xf3, 0x00, 0x08},
        {0x00, 0xfa, 0xf3, 0xfc, 0x9b, 0x61, 0xff, 0x3c, 0x00, 0x00, 0x0e, 0xff, 0x00, 0x0a, 0xff, 0x30, 0x00, 0x00, 0xe0, 0x13, 0x35, 0x74, 0xff, 0x15, 0x00, 0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x08},
        {0x00, 0xfe, 0xfd, 0xc5, 0xff, 0x00, 0xff, 0x0b, 0x00, 0xff, 0x00, 0xff, 0x00, 0x43, 0x00, 0x15, 0x00, 0xff, 0x00, 0x00, 0xea, 0xff, 0x00, 0x00, 0x03, 0xff, 0x01, 0x00, 0x00, 0x01, 0x02, 0x00, 0x08},
        {0x16, 0xff, 0x00, 0x00, 0x00, 0xff, 0x03, 0x05, 0xff, 0x0c, 0x00, 0x00, 0x16, 0x2f, 0xff, 0xfc, 0xfa, 0x00, 0x02, 0x00, 0x56, 0xff, 0x00, 0xff, 0xfa, 0xc3, 0xff, 0xff, 0xe6, 0xeb, 0x29, 0x09, 0x08},
        {0x00, 0xff, 0x00, 0xe7, 0x00, 0xff, 0x12, 0xff, 0xff, 0x00, 0x03, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x87, 0x31, 0x00, 0x17, 0xfe, 0x1a, 0xff, 0x3d, 0x00, 0xd4, 0xfd, 0x01, 0x00, 0x03, 0xfe, 0x08},
        {0x00, 0xff, 0xff, 0xf0, 0x65, 0x61, 0xff, 0xff, 0xf8, 0xb3, 0xff, 0x00, 0xff, 0xff, 0xfe, 0xfe, 0x2a, 0xff, 0xff, 0xd1, 0xff, 0x04, 0xff, 0xff, 0x00, 0x00, 0x00, 0x41, 0x00, 0xf0, 0xff, 0xe3, 0x08},
        {0x00, 0xff, 0x1c, 0x01, 0x14, 0x00, 0x28, 0xff, 0xff, 0x32, 0xea, 0xfc, 0x01, 0xff, 0xff, 0x00, 0x8f, 0x20, 0x00, 0x00, 0x00, 0x05, 0x1e, 0x87, 0xff, 0xff, 0x06, 0x00, 0xff, 0xf8, 0x00, 0x00, 0x08},
        {0xff, 0x02, 0x02, 0x00, 0xfd, 0x1b, 0xff, 0x00, 0xff, 0xf8, 0x00, 0x23, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0xff, 0x58, 0xff, 0x21, 0xff, 0x42, 0x00, 0xf6, 0xff, 0x06, 0x91, 0xff, 0x00, 0x08},
        {0xff, 0xa0, 0xf1, 0x00, 0x6f, 0xfc, 0xe6, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xb3, 0xf9, 0x00, 0x00, 0x00, 0x00, 0x1c, 0x33, 0xd9, 0x00, 0x00, 0x51, 0x81, 0x00, 0xfe, 0xf3, 0x00, 0x00, 0xff, 0x19, 0x08},
        {0x25, 0x00, 0x5e, 0x00, 0x00, 0x00, 0xff, 0x05, 0xff, 0x00, 0xf8, 0x00, 0x00, 0xf9, 0x1c, 0xc4, 0xb9, 0xff, 0xca, 0x00, 0x8d, 0xfe, 0xe3, 0x00, 0x00, 0x00, 0xe4, 0x00, 0x00, 0x2e, 0xdc, 0xc4, 0x08},
        {0x00, 0xff, 0xff, 0xca, 0x92, 0xff, 0xff, 0xff, 0x00, 0xff, 0x01, 0x00, 0xfe, 0xff, 0x00, 0xf5, 0x00, 0x00, 0x00, 0x00, 0xff, 0xfe, 0x0d, 0xff, 0xf2, 0xff, 0x44, 0x00, 0x00, 0xff, 0xfc, 0x03, 0x08},
        {0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0x06, 0x00, 0x00, 0x01, 0x00, 0xfd, 0xff, 0xff, 0x3e, 0x00, 0x00, 0x06, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x00, 0x00, 0xf9, 0xb0, 0xff, 0x3a, 0xe6, 0x07},
        {0x03, 0xff, 0x00, 0xff, 0x01, 0x64, 0xfd, 0x00, 0xff, 0x0b, 0xff, 0xfb, 0x00, 0xff, 0x01, 0x00, 0xfe, 0xff, 0x00, 0x00, 0xff, 0x01, 0x1d, 0x03, 0xfe, 0x0c, 0xff, 0xff, 0x05, 0x0d, 0x04, 0xff, 0x08},
        {0x35, 0xff, 0x00, 0x00, 0x95, 0x00, 0x00, 0xf2, 0xdf, 0xf5, 0xf9, 0x00, 0xff, 0x00, 0x09, 0x00, 0x00, 0x00, 0xff, 0xfe, 0x86, 0x00, 0x32, 0x00, 0xff, 0xff, 0xd2, 0xed, 0x01, 0xff, 0x00, 0x00, 0x08},
        {0xf8, 0x00, 0x17, 0xff, 0x00, 0xff, 0xcc, 0xff, 0x99, 0xac, 0xa3, 0xff, 0xff, 0x23, 0xff, 0x00, 0x00, 0x09, 0xe9, 0x03, 0x02, 0xf1, 0xfd, 0xff, 0x00, 0x0f, 0xff, 0x00, 0x00, 0xff, 0x52, 0x00, 0x08},
        {0xff, 0x03, 0x00, 0x01, 0xfe, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x13, 0xff, 0x00, 0xf7, 0xff, 0xea, 0x02, 0x00, 0xd8, 0x07, 0xff, 0x02, 0xff, 0x00, 0x00, 0xff, 0xff, 0xfd, 0xff, 0xff, 0x07},
        {0x5d, 0xb8, 0x00, 0x00, 0x70, 0xff, 0x00, 0x0b, 0x00, 0x00, 0x00, 0x00, 0xe4, 0x0e, 0x01, 0x03, 0xff, 0x01, 0xff, 0x09, 0xff, 0x00, 0xbd, 0xff, 0xff, 0x3f, 0x00, 0xf5, 0xff, 0x01, 0x00, 0x00, 0x08},
        {0xfe, 0x01, 0xc7, 0xe5, 0x00, 0x9a, 0x00, 0x00, 0x00, 0x00, 0xff, 0x00, 0xff, 0x07, 0xff, 0x03, 0x00, 0x00, 0x99, 0x3c, 0xa4, 0xff, 0x00, 0x00, 0x00, 0x1a, 0x00, 0x04, 0x00, 0xff, 0xb4, 0xfd, 0x08},
        {0xf0, 0xff, 0xff, 0xf9, 0xff, 0xff, 0xee, 0xc2, 0xfe, 0x00, 0xe4, 0x13, 0xe4, 0xfc, 0xff, 0x0f, 0xf0, 0x00, 0x00, 0xe9, 0xff, 0x0e, 0x0b, 0x6b, 0x46, 0x00, 0x21, 0xfd, 0xf9, 0xff, 0x0b, 0xff, 0x07},
        {0xfc, 0x00, 0xff, 0x00, 0x16, 0x63, 0xff, 0x1a, 0x85, 0xd1, 0xff, 0xdf, 0x66, 0x01, 0xff, 0x00, 0x25, 0x00, 0x01, 0x2c, 0x00, 0xff, 0x56, 0x00, 0xff, 0xe8, 0xfc, 0xff, 0x00, 0x00, 0x00, 0xff, 0x08},
        {0xff, 0x00, 0x05, 0xe7, 0x00, 0xff, 0x01, 0xfa, 0x0c, 0x00, 0xff, 0x01, 0xe8, 0x02, 0xff, 0x00, 0xc4, 0x01, 0x00, 0xfd, 0x00, 0x00, 0x16, 0x00, 0xff, 0xff, 0x64, 0x74, 0xb1, 0xff, 0xe1, 0x00, 0x08},
        {0x00, 0x02, 0xff, 0xfc, 0x01, 0x00, 0x55, 0x00, 0xfc, 0x24, 0x00, 0xf5, 0x00, 0x02, 0x3b, 0xac, 0xe9, 0x00, 0xdd, 0xbd, 0xff, 0x60, 0xff, 0x00, 0xfd, 0xf8, 0x00, 0xff, 0xff, 0xff, 0xff, 0xff, 0x06},
        {0x00, 0x00, 0xfe, 0x00, 0x00, 0x00, 0xff, 0x00, 0xfe, 0xff, 0xff, 0xff, 0xff, 0x22, 0xff, 0xff, 0x04, 0xff, 0x00, 0xfc, 0xfe, 0x00, 0x00, 0xff, 0x7f, 0x00, 0xff, 0xff, 0xff, 0xfe, 0xff, 0x00, 0x08},
        {0x00, 0xff, 0x00, 0xff, 0xff, 0xff, 0xfe, 0x05, 0xff, 0x6d, 0xff, 0x00, 0x00, 0xfe, 0x00, 0xfd, 0x00, 0x00, 0x00, 0xff, 0x00, 0xf5, 0x0d, 0x00, 0x57, 0x00, 0x0a, 0xff, 0x06, 0xff, 0xff, 0x00, 0x08},
        {0xd2, 0x07, 0x03, 0x00, 0xff, 0xff, 0x00, 0xfe, 0xf4, 0x00, 0xd9, 0x10, 0x1f, 0x03, 0x00, 0x00, 0x00, 0x4f, 0xfa, 0x00, 0x00, 0xf7, 0x03, 0x01, 0x00, 0x00, 0xff, 0x00, 0xaa, 0xff, 0xff, 0x03, 0x08},
        {0xf3, 0xff, 0xfb, 0xff, 0x02, 0xff, 0xfe, 0xff, 0xc3, 0x26, 0x1a, 0xff, 0x00, 0x1f, 0x8f, 0xef, 0xc8, 0x00, 0x00, 0x00, 0xf9, 0x00, 0x04, 0x05, 0x00, 0x00, 0xd0, 0xff, 0xfe, 0x04, 0x11, 0xda, 0x06}
};


// FP8E4
static uint16_t wt_mhex_fp8e4[32][32] = {
        {0x0006, 0x004b, 0x0050, 0x0086, 0x009a, 0x005e, 0x0047, 0x0090, 0x009c, 0x00d2, 0x00cf, 0x00d0, 0x005c, 0x0043, 0x005c, 0x001d, 0x0047, 0x001b, 0x009f, 0x005f, 0x008e, 0x0094, 0x0054, 0x000d, 0x000e, 0x00d4, 0x00ca, 0x0059, 0x0011, 0x0086, 0x0042, 0x00d0},
        {0x0084, 0x0056, 0x0004, 0x00cd, 0x009e, 0x008c, 0x00c1, 0x0041, 0x00c7, 0x000a, 0x0051, 0x004c, 0x0090, 0x0040, 0x008c, 0x0009, 0x008a, 0x001b, 0x0018, 0x001b, 0x0056, 0x00d0, 0x0091, 0x0086, 0x00c8, 0x00de, 0x001b, 0x0084, 0x00d2, 0x009a, 0x000f, 0x009f},
        {0x00cb, 0x008b, 0x00d9, 0x0000, 0x00d3, 0x0097, 0x001d, 0x00d3, 0x005b, 0x0081, 0x005f, 0x008b, 0x00d3, 0x00da, 0x0056, 0x0086, 0x00dd, 0x0089, 0x001d, 0x00c8, 0x0002, 0x000b, 0x000b, 0x0018, 0x00d8, 0x004b, 0x00cc, 0x000a, 0x0054, 0x0059, 0x0055, 0x00d9},
        {0x008b, 0x004f, 0x005c, 0x004f, 0x0092, 0x0003, 0x0007, 0x00cc, 0x00ce, 0x0090, 0x0042, 0x0006, 0x0053, 0x005c, 0x009e, 0x00da, 0x0015, 0x0000, 0x00c5, 0x008d, 0x0048, 0x0019, 0x0052, 0x0003, 0x0044, 0x00de, 0x00cf, 0x0011, 0x00d0, 0x00c9, 0x000a, 0x00d2},
        {0x00de, 0x0089, 0x00d5, 0x00c1, 0x00c5, 0x001e, 0x00d8, 0x0007, 0x0001, 0x001d, 0x0098, 0x00ce, 0x00df, 0x00cf, 0x0094, 0x0017, 0x005a, 0x0083, 0x00c6, 0x0004, 0x00c7, 0x0042, 0x0094, 0x0019, 0x00c5, 0x00ce, 0x0092, 0x008e, 0x0006, 0x005b, 0x00cb, 0x0005},
        {0x00d5, 0x0001, 0x00d0, 0x009e, 0x00c0, 0x008d, 0x008f, 0x0087, 0x0086, 0x004a, 0x0096, 0x004e, 0x008b, 0x00ca, 0x0042, 0x00dc, 0x00dc, 0x0058, 0x00c5, 0x008a, 0x0089, 0x00cf, 0x0005, 0x0081, 0x0005, 0x0094, 0x0099, 0x0045, 0x0054, 0x0086, 0x0085, 0x000e},
        {0x00cf, 0x0012, 0x0052, 0x005d, 0x0085, 0x00d0, 0x00dd, 0x0092, 0x00cc, 0x0054, 0x0059, 0x00d4, 0x0008, 0x001e, 0x0001, 0x0091, 0x009a, 0x0059, 0x0055, 0x0044, 0x0054, 0x008c, 0x00dc, 0x0083, 0x0094, 0x0096, 0x0057, 0x0045, 0x001a, 0x0098, 0x008f, 0x000c},
        {0x00da, 0x008f, 0x0054, 0x0011, 0x00da, 0x0087, 0x00c2, 0x0057, 0x008e, 0x00cc, 0x0089, 0x000b, 0x0053, 0x0082, 0x001c, 0x004d, 0x0082, 0x0054, 0x00de, 0x004f, 0x0051, 0x00d2, 0x000b, 0x0003, 0x008e, 0x0087, 0x009e, 0x0004, 0x0083, 0x0057, 0x0000, 0x0009},
        {0x000b, 0x00c3, 0x0044, 0x009e, 0x0087, 0x001d, 0x0054, 0x0014, 0x000f, 0x0002, 0x0009, 0x00c5, 0x0083, 0x0054, 0x0012, 0x0050, 0x0006, 0x0014, 0x0015, 0x0093, 0x004a, 0x00d6, 0x00cb, 0x009b, 0x0092, 0x00ca, 0x0007, 0x009f, 0x0017, 0x0099, 0x0011, 0x0058},
        {0x000f, 0x0095, 0x0051, 0x00c1, 0x005c, 0x001e, 0x0050, 0x00d4, 0x00cf, 0x00c3, 0x00cc, 0x009f, 0x000a, 0x0041, 0x00db, 0x0040, 0x00c7, 0x0093, 0x00df, 0x00de, 0x0051, 0x0056, 0x00cb, 0x0041, 0x0083, 0x0042, 0x0052, 0x0002, 0x0041, 0x00d5, 0x0046, 0x004e},
        {0x005a, 0x0051, 0x0010, 0x001b, 0x00da, 0x00d5, 0x0013, 0x0053, 0x0049, 0x0017, 0x009d, 0x00cd, 0x008f, 0x00d3, 0x00d4, 0x00c8, 0x0046, 0x0098, 0x0009, 0x009a, 0x00cf, 0x005c, 0x0093, 0x00d4, 0x00dc, 0x0019, 0x005f, 0x0056, 0x0094, 0x00da, 0x0018, 0x0059},
        {0x0093, 0x008f, 0x0080, 0x00d3, 0x0081, 0x0084, 0x0084, 0x00c5, 0x0003, 0x00de, 0x0055, 0x0001, 0x0040, 0x001f, 0x0042, 0x0087, 0x0044, 0x00c3, 0x000d, 0x0017, 0x008a, 0x0012, 0x005c, 0x00dd, 0x0041, 0x009f, 0x0012, 0x0099, 0x0012, 0x004e, 0x00c2, 0x008a},
        {0x00d2, 0x009a, 0x008a, 0x0084, 0x0019, 0x000b, 0x00db, 0x00c7, 0x00ca, 0x00cf, 0x0049, 0x00d2, 0x00c6, 0x0045, 0x008e, 0x0056, 0x00c0, 0x0055, 0x0080, 0x00c2, 0x0097, 0x0042, 0x0013, 0x005b, 0x00c5, 0x0046, 0x0090, 0x0006, 0x0002, 0x0015, 0x0003, 0x0050},
        {0x0088, 0x005a, 0x004c, 0x0051, 0x008c, 0x001b, 0x0019, 0x0087, 0x0044, 0x0083, 0x0044, 0x005a, 0x004c, 0x0014, 0x00c3, 0x00da, 0x005d, 0x0003, 0x00cf, 0x00c9, 0x00c1, 0x00d9, 0x0014, 0x004f, 0x0017, 0x0097, 0x008a, 0x00cf, 0x0041, 0x00dc, 0x0045, 0x0058},
        {0x0000, 0x00c2, 0x0008, 0x008e, 0x00d4, 0x0095, 0x0090, 0x004e, 0x00c2, 0x0015, 0x0012, 0x00dc, 0x001d, 0x001f, 0x00d6, 0x000c, 0x008e, 0x009c, 0x009e, 0x0000, 0x0046, 0x0056, 0x0092, 0x0096, 0x0007, 0x00cb, 0x0058, 0x008c, 0x0081, 0x0012, 0x005f, 0x009b},
        {0x0096, 0x0003, 0x000e, 0x0059, 0x00c5, 0x001c, 0x0019, 0x00d7, 0x00c3, 0x0047, 0x00dc, 0x0093, 0x00d7, 0x0098, 0x0084, 0x0087, 0x009e, 0x0047, 0x0053, 0x00de, 0x000c, 0x005c, 0x00df, 0x0090, 0x0097, 0x001c, 0x0040, 0x00db, 0x0082, 0x001e, 0x00d6, 0x0053},
        {0x0016, 0x00d1, 0x0045, 0x0016, 0x00db, 0x0092, 0x004d, 0x00d2, 0x00ce, 0x0043, 0x000c, 0x0089, 0x009f, 0x0053, 0x004e, 0x0082, 0x0055, 0x0007, 0x0015, 0x00d8, 0x0087, 0x0053, 0x0016, 0x0085, 0x0050, 0x0099, 0x0016, 0x0054, 0x008b, 0x0081, 0x00d7, 0x0080},
        {0x009a, 0x008c, 0x0005, 0x00d1, 0x00cb, 0x008d, 0x00c6, 0x0056, 0x0017, 0x00d7, 0x0085, 0x0016, 0x009a, 0x0019, 0x0087, 0x00ca, 0x00cf, 0x000d, 0x0006, 0x0018, 0x00c1, 0x0086, 0x005f, 0x00ca, 0x008d, 0x008b, 0x0089, 0x005b, 0x0051, 0x0098, 0x0099, 0x0004},
        {0x0098, 0x0096, 0x000c, 0x0051, 0x0005, 0x0050, 0x0007, 0x0047, 0x00ce, 0x0049, 0x0018, 0x005d, 0x0046, 0x008c, 0x00d7, 0x0056, 0x0044, 0x005a, 0x008b, 0x0044, 0x0042, 0x00df, 0x0019, 0x00d8, 0x00d4, 0x0048, 0x004c, 0x00cf, 0x004f, 0x0046, 0x0086, 0x0084},
        {0x0088, 0x00d2, 0x00d0, 0x009b, 0x0090, 0x009a, 0x001a, 0x00dc, 0x001c, 0x0092, 0x009e, 0x0040, 0x00ca, 0x00ca, 0x004f, 0x00ce, 0x00d7, 0x004b, 0x00de, 0x0053, 0x001d, 0x0099, 0x001b, 0x0091, 0x008b, 0x0046, 0x009f, 0x00c2, 0x0098, 0x0049, 0x004b, 0x004d},
        {0x0018, 0x00c9, 0x00cd, 0x004e, 0x0012, 0x0005, 0x0047, 0x005c, 0x00df, 0x0048, 0x001f, 0x001c, 0x0081, 0x0080, 0x00c0, 0x0096, 0x00c7, 0x00c7, 0x0017, 0x004f, 0x00c4, 0x00cd, 0x0049, 0x0045, 0x0080, 0x0096, 0x0053, 0x00cd, 0x0012, 0x0000, 0x0009, 0x0081},
        {0x0046, 0x000e, 0x0007, 0x0090, 0x004f, 0x0057, 0x001e, 0x00ca, 0x00c6, 0x0008, 0x0007, 0x00c4, 0x0053, 0x0050, 0x00dd, 0x0018, 0x0042, 0x0016, 0x005a, 0x00d8, 0x0087, 0x0056, 0x00d9, 0x008d, 0x008b, 0x0091, 0x004d, 0x001d, 0x009a, 0x00c2, 0x00c1, 0x005d},
        {0x0007, 0x0045, 0x005f, 0x008a, 0x009f, 0x0059, 0x00d4, 0x0053, 0x0056, 0x005e, 0x0085, 0x0041, 0x0092, 0x0018, 0x0058, 0x004d, 0x0012, 0x008b, 0x005c, 0x008f, 0x0010, 0x00db, 0x00c4, 0x00dc, 0x00da, 0x0059, 0x0087, 0x00c9, 0x0000, 0x0088, 0x009e, 0x00da},
        {0x000f, 0x0014, 0x0014, 0x008c, 0x00c8, 0x0049, 0x0082, 0x00d6, 0x0011, 0x0047, 0x0007, 0x0085, 0x00ce, 0x009d, 0x0058, 0x0057, 0x00da, 0x001f, 0x0006, 0x0081, 0x008e, 0x0088, 0x0017, 0x0014, 0x00d8, 0x0095, 0x00c8, 0x00d8, 0x001a, 0x004b, 0x0006, 0x008b},
        {0x0056, 0x00c7, 0x0090, 0x00c0, 0x00d8, 0x0015, 0x0003, 0x00c3, 0x004e, 0x009d, 0x00df, 0x0045, 0x008d, 0x004f, 0x0081, 0x00c8, 0x0046, 0x0017, 0x00cb, 0x0080, 0x0015, 0x0010, 0x001a, 0x00cf, 0x0095, 0x004c, 0x0056, 0x004b, 0x00ca, 0x00cf, 0x0091, 0x0082},
        {0x009e, 0x001c, 0x0092, 0x004c, 0x0004, 0x0040, 0x0094, 0x005b, 0x0013, 0x001a, 0x005a, 0x0055, 0x004d, 0x0006, 0x00c3, 0x0057, 0x0043, 0x00c0, 0x00d6, 0x0046, 0x004d, 0x0054, 0x0043, 0x0089, 0x000b, 0x000f, 0x0056, 0x00dd, 0x0011, 0x001e, 0x0048, 0x0004},
        {0x008d, 0x0088, 0x00cf, 0x00d6, 0x0008, 0x001d, 0x008a, 0x0051, 0x0040, 0x00ca, 0x00d5, 0x008b, 0x0055, 0x008c, 0x00d1, 0x004a, 0x00cd, 0x0012, 0x0097, 0x0014, 0x00c3, 0x001d, 0x0096, 0x00ca, 0x000f, 0x0051, 0x005b, 0x0003, 0x001f, 0x005b, 0x000f, 0x00df},
        {0x0059, 0x0018, 0x005f, 0x0091, 0x00da, 0x0017, 0x0083, 0x00c7, 0x0058, 0x004a, 0x0081, 0x0088, 0x0003, 0x001f, 0x00c6, 0x00d8, 0x004a, 0x00db, 0x0084, 0x0086, 0x0095, 0x00c5, 0x0019, 0x0056, 0x0056, 0x0012, 0x0017, 0x0086, 0x0040, 0x0006, 0x0056, 0x00de},
        {0x004d, 0x00de, 0x00d2, 0x0059, 0x004b, 0x004a, 0x0044, 0x000d, 0x008e, 0x0056, 0x0002, 0x0043, 0x0055, 0x005d, 0x00c3, 0x0044, 0x00d8, 0x005b, 0x0018, 0x0019, 0x005e, 0x00d7, 0x00c0, 0x000d, 0x0058, 0x009e, 0x0005, 0x0087, 0x0048, 0x00d7, 0x000e, 0x0088},
        {0x008a, 0x0093, 0x005c, 0x004e, 0x001f, 0x008d, 0x000d, 0x0057, 0x004a, 0x0058, 0x0095, 0x004c, 0x001a, 0x0004, 0x004e, 0x005d, 0x00c8, 0x001c, 0x0042, 0x00d1, 0x00c2, 0x0092, 0x004e, 0x0044, 0x009d, 0x00c2, 0x004e, 0x009a, 0x00cd, 0x0057, 0x0089, 0x00c9},
        {0x00d7, 0x0057, 0x0040, 0x0002, 0x0093, 0x004c, 0x004d, 0x00c7, 0x009e, 0x0002, 0x000a, 0x0040, 0x0096, 0x0090, 0x001f, 0x0041, 0x0000, 0x0092, 0x0094, 0x0080, 0x0047, 0x0009, 0x008b, 0x000d, 0x0057, 0x00d2, 0x0005, 0x00d3, 0x00c3, 0x0059, 0x0089, 0x0087},
        {0x0007, 0x0015, 0x004e, 0x0092, 0x00df, 0x0054, 0x001c, 0x00ca, 0x005d, 0x00d6, 0x00d4, 0x0017, 0x00d7, 0x004b, 0x005b, 0x001f, 0x008b, 0x00c6, 0x0010, 0x0006, 0x005a, 0x0087, 0x0017, 0x0084, 0x000b, 0x008f, 0x00ce, 0x00c6, 0x0086, 0x000d, 0x001c, 0x009d}
};


/* 
  Test Failed: Output mismatch for FP8E4 Conversion.
    Mismatch at row 11, col 2: Expected 0x0080, Got 0x0000
    Mismatch at row 12, col 18: Expected 0x0080, Got 0x0000
    Mismatch at row 16, col 31: Expected 0x0080, Got 0x0000
    Mismatch at row 20, col 13: Expected 0x0080, Got 0x0000
    Mismatch at row 20, col 24: Expected 0x0080, Got 0x0000
    Mismatch at row 24, col 19: Expected 0x0080, Got 0x0000
    Mismatch at row 30, col 19: Expected 0x0080, Got 0x0000
测试发现fp8e4 mhex中存在几组数据经过float_data_align后与参考wt_algn不一致，经查阅资料，发现fp8e4m3中0x80表示-0,0x00表示+0;计算中无区别
 */

static uint16_t wt_algn_fp8e4[32][33] = {
        {0x00, 0x16, 0x20, 0xff, 0xff, 0x70, 0x0f, 0xff, 0xff, 0xd8, 0xe2, 0xe0, 0x60, 0x0b, 0x60, 0x00, 0x0f, 0x00, 0xff, 0x78, 0xff, 0xff, 0x30, 0x00, 0x00, 0xd0, 0xec, 0x48, 0x00, 0xff, 0x0a, 0xe0, 0x04},
        {0xff, 0x38, 0x00, 0xe6, 0xff, 0xff, 0xf7, 0x09, 0xf1, 0x00, 0x24, 0x18, 0xff, 0x08, 0xff, 0x00, 0xff, 0x00, 0x00, 0x00, 0x38, 0xe0, 0xff, 0xff, 0xf0, 0x90, 0x00, 0xff, 0xd8, 0xff, 0x00, 0xff, 0x04},
        {0xea, 0xff, 0xb8, 0x00, 0xd4, 0xff, 0x00, 0xd4, 0x58, 0xff, 0x78, 0xff, 0xd4, 0xb0, 0x38, 0xff, 0x98, 0xff, 0x00, 0xf0, 0x00, 0x00, 0x00, 0x00, 0xc0, 0x16, 0xe8, 0x00, 0x30, 0x48, 0x34, 0xb8, 0x04},
        {0xff, 0x1e, 0x60, 0x1e, 0xff, 0x00, 0x00, 0xe8, 0xe4, 0xff, 0x0a, 0x00, 0x2c, 0x60, 0xff, 0xb0, 0x00, 0x00, 0xf3, 0xff, 0x10, 0x00, 0x28, 0x00, 0x0c, 0x90, 0xe2, 0x00, 0xe0, 0xee, 0x00, 0xd8, 0x04},
        {0x90, 0xff, 0xcc, 0xf7, 0xf3, 0x00, 0xc0, 0x00, 0x00, 0x00, 0xff, 0xe4, 0x88, 0xe2, 0xff, 0x00, 0x50, 0xff, 0xf2, 0x00, 0xf1, 0x0a, 0xff, 0x00, 0xf3, 0xe4, 0xff, 0xff, 0x00, 0x58, 0xea, 0x00, 0x04},
        {0xcc, 0x00, 0xe0, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0x14, 0xff, 0x1c, 0xff, 0xec, 0x0a, 0xa0, 0xa0, 0x40, 0xf3, 0xff, 0xff, 0xe2, 0x00, 0xff, 0x00, 0xff, 0xff, 0x0d, 0x30, 0xff, 0xff, 0x00, 0x04},
        {0xe2, 0x00, 0x28, 0x68, 0xff, 0xe0, 0x98, 0xff, 0xe8, 0x30, 0x48, 0xd0, 0x00, 0x00, 0x00, 0xff, 0xff, 0x48, 0x34, 0x0c, 0x30, 0xff, 0xa0, 0xff, 0xff, 0xff, 0x3c, 0x0d, 0x00, 0xff, 0xff, 0x00, 0x04},
        {0xb0, 0xff, 0x30, 0x00, 0xb0, 0xff, 0xf6, 0x3c, 0xff, 0xe8, 0xff, 0x00, 0x2c, 0xff, 0x00, 0x1a, 0xff, 0x30, 0x90, 0x1e, 0x24, 0xd8, 0x00, 0x00, 0xff, 0xff, 0xff, 0x00, 0xff, 0x3c, 0x00, 0x00, 0x04},
        {0x00, 0xf5, 0x0c, 0xff, 0xff, 0x00, 0x30, 0x00, 0x00, 0x00, 0x00, 0xf3, 0xff, 0x30, 0x00, 0x20, 0x00, 0x00, 0x00, 0xff, 0x14, 0xc8, 0xea, 0xff, 0xff, 0xec, 0x00, 0xff, 0x00, 0xff, 0x00, 0x40, 0x04},
        {0x00, 0xff, 0x24, 0xf7, 0x60, 0x00, 0x20, 0xd0, 0xe2, 0xf5, 0xe8, 0xff, 0x00, 0x09, 0xa8, 0x08, 0xf1, 0xff, 0x88, 0x90, 0x24, 0x38, 0xea, 0x09, 0xff, 0x0a, 0x28, 0x00, 0x09, 0xcc, 0x0e, 0x1c, 0x04},
        {0x50, 0x24, 0x00, 0x00, 0xb0, 0xcc, 0x00, 0x2c, 0x12, 0x00, 0xff, 0xe6, 0xff, 0xd4, 0xd0, 0xf0, 0x0e, 0xff, 0x00, 0xff, 0xe2, 0x60, 0xff, 0xd0, 0xa0, 0x00, 0x78, 0x38, 0xff, 0xb0, 0x00, 0x48, 0x04},
        {0xff, 0xff, 0x80, 0xd4, 0xff, 0xff, 0xff, 0xf3, 0x00, 0x90, 0x34, 0x00, 0x08, 0x00, 0x0a, 0xff, 0x0c, 0xf5, 0x00, 0x00, 0xff, 0x00, 0x60, 0x98, 0x09, 0xff, 0x00, 0xff, 0x00, 0x1c, 0xf6, 0xff, 0x04},
        {0xd8, 0xff, 0xff, 0xff, 0x00, 0x00, 0xa8, 0xf1, 0xec, 0xe2, 0x12, 0xd8, 0xf2, 0x0d, 0xff, 0x38, 0xf8, 0x34, 0x80, 0xf6, 0xff, 0x0a, 0x00, 0x58, 0xf3, 0x0e, 0xff, 0x00, 0x00, 0x00, 0x00, 0x20, 0x04},
        {0xff, 0x50, 0x18, 0x24, 0xff, 0x00, 0x00, 0xff, 0x0c, 0xff, 0x0c, 0x50, 0x18, 0x00, 0xf5, 0xb0, 0x68, 0x00, 0xe2, 0xee, 0xf7, 0xb8, 0x00, 0x1e, 0x00, 0xff, 0xff, 0xe2, 0x09, 0xa0, 0x0d, 0x40, 0x04},
        {0x00, 0xf6, 0x00, 0xff, 0xd0, 0xff, 0xff, 0x1c, 0xf6, 0x00, 0x00, 0xa0, 0x00, 0x00, 0xc8, 0x00, 0xff, 0xff, 0xff, 0x00, 0x0e, 0x38, 0xff, 0xff, 0x00, 0xea, 0x40, 0xff, 0xff, 0x00, 0x78, 0xff, 0x04},
        {0xff, 0x00, 0x00, 0x48, 0xf3, 0x00, 0x00, 0xc4, 0xf5, 0x0f, 0xa0, 0xff, 0xc4, 0xff, 0xff, 0xff, 0xff, 0x0f, 0x2c, 0x90, 0x00, 0x60, 0x88, 0xff, 0xff, 0x00, 0x08, 0xa8, 0xff, 0x00, 0xc8, 0x2c, 0x04},
        {0x00, 0xdc, 0x0d, 0x00, 0xa8, 0xff, 0x1a, 0xd8, 0xe4, 0x0b, 0x00, 0xff, 0xff, 0x2c, 0x1c, 0xff, 0x34, 0x00, 0x00, 0xc0, 0xff, 0x2c, 0x00, 0xff, 0x20, 0xff, 0x00, 0x30, 0xff, 0xff, 0xc4, 0x80, 0x04},
        {0xff, 0xff, 0x00, 0xdc, 0xea, 0xff, 0xf2, 0x38, 0x00, 0xc4, 0xff, 0x00, 0xff, 0x00, 0xff, 0xec, 0xe2, 0x00, 0x00, 0x00, 0xf7, 0xff, 0x78, 0xec, 0xff, 0xff, 0xff, 0x58, 0x24, 0xff, 0xff, 0x00, 0x04},
        {0xff, 0xff, 0x00, 0x24, 0x00, 0x20, 0x00, 0x0f, 0xe4, 0x12, 0x00, 0x68, 0x0e, 0xff, 0xc4, 0x38, 0x0c, 0x50, 0xff, 0x0c, 0x0a, 0x88, 0x00, 0xc0, 0xd0, 0x10, 0x18, 0xe2, 0x1e, 0x0e, 0xff, 0xff, 0x04},
        {0xff, 0xd8, 0xe0, 0xff, 0xff, 0xff, 0x00, 0xa0, 0x00, 0xff, 0xff, 0x08, 0xec, 0xec, 0x1e, 0xe4, 0xc4, 0x16, 0x90, 0x2c, 0x00, 0xff, 0x00, 0xff, 0xff, 0x0e, 0xff, 0xf6, 0xff, 0x12, 0x16, 0x1a, 0x04},
        {0x00, 0xee, 0xe6, 0x1c, 0x00, 0x00, 0x0f, 0x60, 0x88, 0x10, 0x00, 0x00, 0xff, 0x80, 0xf8, 0xff, 0xf1, 0xf1, 0x00, 0x1e, 0xf4, 0xe6, 0x12, 0x0d, 0x80, 0xff, 0x2c, 0xe6, 0x00, 0x00, 0x00, 0xff, 0x04},
        {0x0e, 0x00, 0x00, 0xff, 0x1e, 0x3c, 0x00, 0xec, 0xf2, 0x00, 0x00, 0xf4, 0x2c, 0x20, 0x98, 0x00, 0x0a, 0x00, 0x50, 0xc0, 0xff, 0x38, 0xb8, 0xff, 0xff, 0xff, 0x1a, 0x00, 0xff, 0xf6, 0xf7, 0x68, 0x04},
        {0x00, 0x0d, 0x78, 0xff, 0xff, 0x48, 0xd0, 0x2c, 0x38, 0x70, 0xff, 0x09, 0xff, 0x00, 0x40, 0x1a, 0x00, 0xff, 0x60, 0xff, 0x00, 0xa8, 0xf4, 0xa0, 0xb0, 0x48, 0xff, 0xee, 0x00, 0xff, 0xff, 0xb0, 0x04},
        {0x00, 0x00, 0x00, 0xff, 0xf0, 0x12, 0xff, 0xc8, 0x00, 0x0f, 0x00, 0xff, 0xe4, 0xff, 0x40, 0x3c, 0xb0, 0x00, 0x00, 0xff, 0xff, 0xff, 0x00, 0x00, 0xc0, 0xff, 0xf0, 0xc0, 0x00, 0x16, 0x00, 0xff, 0x04},
        {0x38, 0xf1, 0xff, 0xf8, 0xc0, 0x00, 0x00, 0xf5, 0x1c, 0xff, 0x88, 0x0d, 0xff, 0x1e, 0xff, 0xf0, 0x0e, 0x00, 0xea, 0x80, 0x00, 0x00, 0x00, 0xe2, 0xff, 0x18, 0x38, 0x16, 0xec, 0xe2, 0xff, 0xff, 0x04},
        {0xff, 0x00, 0xff, 0x18, 0x00, 0x08, 0xff, 0x58, 0x00, 0x00, 0x50, 0x34, 0x1a, 0x00, 0xf5, 0x3c, 0x0b, 0xf8, 0xc8, 0x0e, 0x1a, 0x30, 0x0b, 0xff, 0x00, 0x00, 0x38, 0x98, 0x00, 0x00, 0x10, 0x00, 0x04},
        {0xff, 0xff, 0xe2, 0xc8, 0x00, 0x00, 0xff, 0x24, 0x08, 0xec, 0xcc, 0xff, 0x34, 0xff, 0xdc, 0x14, 0xe6, 0x00, 0xff, 0x00, 0xf5, 0x00, 0xff, 0xec, 0x00, 0x24, 0x58, 0x00, 0x00, 0x58, 0x00, 0x88, 0x04},
        {0x48, 0x00, 0x78, 0xff, 0xb0, 0x00, 0xff, 0xf1, 0x40, 0x14, 0xff, 0xff, 0x00, 0x00, 0xf2, 0xc0, 0x14, 0xa8, 0xff, 0xff, 0xff, 0xf3, 0x00, 0x38, 0x38, 0x00, 0x00, 0xff, 0x08, 0x00, 0x38, 0x90, 0x04},
        {0x1a, 0x90, 0xd8, 0x48, 0x16, 0x14, 0x0c, 0x00, 0xff, 0x38, 0x00, 0x0b, 0x34, 0x68, 0xf5, 0x0c, 0xc0, 0x58, 0x00, 0x00, 0x70, 0xc4, 0xf8, 0x00, 0x40, 0xff, 0x00, 0xff, 0x10, 0xc4, 0x00, 0xff, 0x04},
        {0xff, 0xff, 0x60, 0x1c, 0x00, 0xff, 0x00, 0x3c, 0x14, 0x40, 0xff, 0x18, 0x00, 0x00, 0x1c, 0x68, 0xf0, 0x00, 0x0a, 0xdc, 0xf6, 0xff, 0x1c, 0x0c, 0xff, 0xf6, 0x1c, 0xff, 0xe6, 0x3c, 0xff, 0xee, 0x04},
        {0xc4, 0x3c, 0x08, 0x00, 0xff, 0x18, 0x1a, 0xf1, 0xff, 0x00, 0x00, 0x08, 0xff, 0xff, 0x00, 0x09, 0x00, 0xff, 0xff, 0x80, 0x0f, 0x00, 0xff, 0x00, 0x3c, 0xd8, 0x00, 0xd4, 0xf5, 0x48, 0xff, 0xff, 0x04},
        {0x00, 0x00, 0x1c, 0xff, 0x88, 0x30, 0x00, 0xec, 0x68, 0xc8, 0xd0, 0x00, 0xc4, 0x16, 0x58, 0x00, 0xff, 0xf2, 0x00, 0x00, 0x50, 0xff, 0x00, 0xff, 0x00, 0xff, 0xe4, 0xf2, 0xff, 0x00, 0x00, 0xff, 0x04}
};


// FP8E5
static uint16_t wt_mhex_fp8e5[32][32] = {
        {0x00c1, 0x0031, 0x0020, 0x00ba, 0x00cc, 0x00b4, 0x00c4, 0x009b, 0x00cf, 0x001c, 0x002b, 0x004e, 0x00c2, 0x0092, 0x00ae, 0x0020, 0x0031, 0x00a5, 0x00d3, 0x0012, 0x004c, 0x0023, 0x00a2, 0x001c, 0x001f, 0x0050, 0x002a, 0x00bf, 0x002a, 0x0039, 0x004f, 0x00bf},
        {0x00ae, 0x0029, 0x0029, 0x0052, 0x00b5, 0x00ce, 0x00d1, 0x00c6, 0x0021, 0x0041, 0x00c3, 0x001d, 0x00a5, 0x00c1, 0x0098, 0x00c4, 0x00bc, 0x0029, 0x00c7, 0x00c0, 0x00af, 0x003b, 0x0035, 0x00a4, 0x00b0, 0x0051, 0x0041, 0x0047, 0x004a, 0x001f, 0x00c3, 0x0035},
        {0x00b2, 0x002e, 0x004a, 0x0025, 0x0037, 0x00c1, 0x0048, 0x009b, 0x0038, 0x0023, 0x00d2, 0x00b3, 0x00af, 0x0027, 0x0026, 0x0096, 0x00cb, 0x004d, 0x0024, 0x0093, 0x00a7, 0x004a, 0x00bf, 0x0024, 0x0038, 0x0046, 0x0095, 0x00b7, 0x00b8, 0x0020, 0x003e, 0x003a},
        {0x001b, 0x004a, 0x0022, 0x0046, 0x00a7, 0x00ad, 0x0033, 0x004c, 0x00d3, 0x00c5, 0x004b, 0x0014, 0x0044, 0x00c7, 0x0042, 0x00a7, 0x00bf, 0x00c5, 0x00b5, 0x00ad, 0x002f, 0x0012, 0x004d, 0x002a, 0x00a8, 0x0050, 0x00c4, 0x0091, 0x0036, 0x00c3, 0x009c, 0x00cc},
        {0x00cc, 0x004a, 0x004b, 0x004d, 0x00a3, 0x00cb, 0x00cb, 0x0024, 0x00b6, 0x00a4, 0x00bb, 0x003b, 0x00cc, 0x00a6, 0x00be, 0x00d2, 0x0025, 0x0046, 0x009c, 0x0096, 0x00af, 0x0023, 0x00b1, 0x0023, 0x00b2, 0x0020, 0x00b8, 0x00b5, 0x0042, 0x0025, 0x00c5, 0x00ba},
        {0x001d, 0x009e, 0x009f, 0x0096, 0x0013, 0x0091, 0x0099, 0x001a, 0x009e, 0x00b1, 0x003e, 0x004f, 0x0098, 0x00b3, 0x0093, 0x0027, 0x00ae, 0x0024, 0x0098, 0x0040, 0x0097, 0x00b4, 0x00a9, 0x002c, 0x00aa, 0x001f, 0x00b0, 0x00b7, 0x009f, 0x0031, 0x0097, 0x009c},
        {0x00ce, 0x009f, 0x00a6, 0x0045, 0x00a9, 0x0052, 0x00d3, 0x0020, 0x0046, 0x009c, 0x004e, 0x00bd, 0x004a, 0x00b3, 0x001b, 0x00b4, 0x00af, 0x0027, 0x00c5, 0x0019, 0x00b4, 0x0021, 0x00a3, 0x00b2, 0x00c1, 0x00c7, 0x003e, 0x00ab, 0x00b5, 0x00a6, 0x0043, 0x00a6},
        {0x001c, 0x00bf, 0x00d3, 0x00b4, 0x001e, 0x0018, 0x00af, 0x009a, 0x003f, 0x00af, 0x002e, 0x0016, 0x00a4, 0x00b8, 0x0029, 0x00b3, 0x00c4, 0x0033, 0x00a3, 0x00a8, 0x004e, 0x00b5, 0x00a6, 0x0039, 0x0027, 0x002c, 0x009d, 0x00b9, 0x0018, 0x0030, 0x0098, 0x00bd},
        {0x0020, 0x0022, 0x00c1, 0x003c, 0x0014, 0x009d, 0x0044, 0x009c, 0x0012, 0x003c, 0x00cf, 0x0040, 0x002e, 0x00ba, 0x0045, 0x00cd, 0x00b6, 0x0039, 0x001b, 0x002f, 0x0049, 0x0025, 0x0043, 0x00ca, 0x0039, 0x00a9, 0x0097, 0x00ad, 0x00a7, 0x00d0, 0x0046, 0x001b},
        {0x0046, 0x00b6, 0x00d2, 0x00a8, 0x0034, 0x001f, 0x00cf, 0x0044, 0x00cb, 0x00c2, 0x009e, 0x0099, 0x0035, 0x0045, 0x002d, 0x002c, 0x0036, 0x00c8, 0x00b6, 0x00a1, 0x0022, 0x002f, 0x003d, 0x00b1, 0x002e, 0x0017, 0x00b8, 0x0020, 0x0033, 0x003f, 0x0035, 0x00b1},
        {0x0049, 0x0014, 0x00a8, 0x0030, 0x0019, 0x0013, 0x0052, 0x00a0, 0x00d2, 0x003c, 0x00ab, 0x0010, 0x002b, 0x00a4, 0x002d, 0x00a7, 0x0045, 0x00ae, 0x00a7, 0x009d, 0x0020, 0x004b, 0x0053, 0x0032, 0x00a0, 0x00d0, 0x001e, 0x00bc, 0x00bb, 0x0014, 0x003a, 0x003e},
        {0x009e, 0x00a6, 0x00d1, 0x00c4, 0x0040, 0x0026, 0x009d, 0x00ad, 0x0038, 0x00c6, 0x00af, 0x004b, 0x003c, 0x00aa, 0x0022, 0x00bd, 0x003c, 0x0039, 0x0026, 0x0035, 0x0018, 0x00ba, 0x001b, 0x00c3, 0x0032, 0x00ba, 0x00a3, 0x0030, 0x00bb, 0x0034, 0x00c1, 0x0041},
        {0x004e, 0x0016, 0x00ad, 0x0019, 0x004e, 0x0023, 0x0032, 0x0013, 0x00a8, 0x00bb, 0x0042, 0x001c, 0x00a6, 0x003b, 0x0097, 0x00aa, 0x00d3, 0x00a9, 0x0043, 0x003b, 0x0042, 0x0041, 0x0021, 0x0026, 0x00b7, 0x0028, 0x00ce, 0x0016, 0x0032, 0x00a9, 0x0023, 0x0040},
        {0x00ae, 0x00d3, 0x00b9, 0x009b, 0x00a4, 0x00b9, 0x00ae, 0x00a5, 0x002b, 0x0030, 0x00be, 0x00ac, 0x00a2, 0x0032, 0x00ac, 0x004d, 0x00b7, 0x00c4, 0x00a8, 0x009a, 0x004b, 0x0090, 0x0029, 0x00d1, 0x00a0, 0x003f, 0x003a, 0x00ce, 0x00c1, 0x00ce, 0x0018, 0x00ca},
        {0x00c1, 0x0019, 0x00a5, 0x003b, 0x00ce, 0x0036, 0x00bf, 0x00b5, 0x00b8, 0x001e, 0x00c7, 0x0042, 0x00c0, 0x0020, 0x00bd, 0x00b9, 0x003f, 0x0043, 0x00b3, 0x0028, 0x0020, 0x00c3, 0x004d, 0x00bc, 0x00a7, 0x004f, 0x00b3, 0x0024, 0x00c9, 0x0022, 0x002d, 0x0036},
        {0x0047, 0x009f, 0x00bb, 0x00bf, 0x00b9, 0x00b5, 0x0045, 0x00c3, 0x0048, 0x00a9, 0x003d, 0x0027, 0x001d, 0x00ac, 0x00a5, 0x00a6, 0x00bd, 0x0092, 0x0097, 0x00c7, 0x00ab, 0x0018, 0x0032, 0x00be, 0x004d, 0x002c, 0x00b0, 0x00c5, 0x00ae, 0x00a0, 0x00a0, 0x0091},
        {0x009b, 0x00cd, 0x001e, 0x00c7, 0x00a6, 0x001a, 0x004d, 0x0047, 0x00bd, 0x0041, 0x004a, 0x00d1, 0x0028, 0x0029, 0x0028, 0x0037, 0x0036, 0x0024, 0x004c, 0x0040, 0x00b9, 0x00b9, 0x00cb, 0x0098, 0x001a, 0x004e, 0x00ab, 0x0023, 0x004d, 0x0019, 0x00a9, 0x00b8},
        {0x00a0, 0x0042, 0x00c8, 0x00bd, 0x00cf, 0x00a6, 0x004a, 0x0027, 0x00c9, 0x002d, 0x0051, 0x00ad, 0x0042, 0x0021, 0x0098, 0x00d1, 0x00a1, 0x001e, 0x003e, 0x00ba, 0x004a, 0x0022, 0x00ce, 0x002a, 0x003f, 0x0025, 0x009b, 0x0026, 0x00b2, 0x0091, 0x00b9, 0x009a},
        {0x00b6, 0x0039, 0x002b, 0x00ad, 0x00ad, 0x0045, 0x003c, 0x0029, 0x00b5, 0x0031, 0x004e, 0x0053, 0x003f, 0x00ae, 0x0040, 0x0053, 0x009a, 0x00be, 0x004c, 0x0090, 0x003c, 0x002e, 0x0049, 0x0044, 0x0016, 0x00d2, 0x0091, 0x00c1, 0x009f, 0x0025, 0x0033, 0x0034},
        {0x00a6, 0x0049, 0x009e, 0x00d1, 0x00c9, 0x00bf, 0x00c9, 0x0020, 0x00bf, 0x00cd, 0x00c0, 0x004e, 0x0047, 0x00b1, 0x0013, 0x00ce, 0x00cd, 0x00c7, 0x001b, 0x001f, 0x0027, 0x00bf, 0x001f, 0x0035, 0x00c6, 0x0026, 0x0036, 0x0091, 0x00c9, 0x0094, 0x001f, 0x002d},
        {0x00b6, 0x00b7, 0x00bc, 0x00a4, 0x0039, 0x00a1, 0x00b2, 0x0031, 0x003c, 0x00cd, 0x00b2, 0x00ac, 0x00c7, 0x00b0, 0x00c4, 0x0037, 0x0094, 0x0045, 0x009d, 0x00d2, 0x00ab, 0x0019, 0x009c, 0x00be, 0x00af, 0x004f, 0x0029, 0x00b2, 0x001c, 0x00bf, 0x0017, 0x00ba},
        {0x009e, 0x0098, 0x00c3, 0x0033, 0x00a9, 0x003a, 0x0044, 0x003f, 0x0093, 0x0033, 0x0034, 0x00a1, 0x0035, 0x0038, 0x001b, 0x002f, 0x002d, 0x0019, 0x0051, 0x001b, 0x0048, 0x0018, 0x009c, 0x00a1, 0x00cd, 0x001d, 0x004c, 0x002c, 0x00b4, 0x00cf, 0x00a0, 0x00a9},
        {0x00ac, 0x002f, 0x0031, 0x00c9, 0x002e, 0x00be, 0x00ab, 0x0024, 0x0031, 0x003e, 0x0031, 0x00b4, 0x0033, 0x0028, 0x00c8, 0x0047, 0x003a, 0x00a4, 0x00c9, 0x0011, 0x004c, 0x003b, 0x001d, 0x0099, 0x0017, 0x0017, 0x00b3, 0x0018, 0x00a9, 0x0015, 0x0015, 0x00a4},
        {0x00bb, 0x00cf, 0x0045, 0x00c0, 0x001c, 0x00c7, 0x003e, 0x00ab, 0x0024, 0x00bc, 0x0012, 0x0037, 0x0097, 0x00ac, 0x0093, 0x00ba, 0x00c4, 0x0099, 0x003f, 0x0049, 0x00b6, 0x001c, 0x009a, 0x0099, 0x0050, 0x0053, 0x001d, 0x00a3, 0x0019, 0x0050, 0x003d, 0x001e},
        {0x0043, 0x004c, 0x00a9, 0x00c4, 0x0098, 0x0038, 0x00cb, 0x00cf, 0x0027, 0x009a, 0x002c, 0x0018, 0x00cb, 0x002d, 0x0012, 0x002d, 0x0040, 0x0013, 0x0095, 0x00cf, 0x0020, 0x0016, 0x0025, 0x0097, 0x003b, 0x00a3, 0x0012, 0x00c1, 0x001d, 0x00c8, 0x00a4, 0x009f},
        {0x0099, 0x001b, 0x00cb, 0x0021, 0x0017, 0x00be, 0x00b1, 0x001d, 0x001b, 0x001f, 0x0046, 0x00bb, 0x00a9, 0x00bb, 0x0030, 0x0097, 0x00ae, 0x0033, 0x0024, 0x003b, 0x0037, 0x00cb, 0x00bc, 0x0042, 0x0010, 0x0029, 0x0013, 0x0053, 0x00ab, 0x001c, 0x003d, 0x003f},
        {0x00b7, 0x00b0, 0x0052, 0x0024, 0x00aa, 0x00a0, 0x00a2, 0x00a5, 0x00bf, 0x003d, 0x0019, 0x004d, 0x00c9, 0x004f, 0x003b, 0x00b4, 0x00b1, 0x0023, 0x00ae, 0x0050, 0x00ac, 0x0037, 0x00ac, 0x0094, 0x00b9, 0x00a3, 0x00a8, 0x0027, 0x001e, 0x00b3, 0x00bb, 0x00c7},
        {0x002b, 0x001e, 0x0013, 0x003c, 0x00ab, 0x00be, 0x0096, 0x00bd, 0x00a0, 0x0035, 0x00b2, 0x003d, 0x00b7, 0x0098, 0x001c, 0x003f, 0x0031, 0x00c5, 0x00c7, 0x003c, 0x0092, 0x0090, 0x00a3, 0x00b0, 0x00a6, 0x00b7, 0x00c9, 0x0043, 0x0031, 0x00c5, 0x00ca, 0x0016},
        {0x0037, 0x003c, 0x00a2, 0x00b3, 0x0038, 0x00ab, 0x00c7, 0x003a, 0x004a, 0x0038, 0x00b0, 0x003a, 0x001e, 0x0039, 0x0045, 0x00cf, 0x0094, 0x003d, 0x0026, 0x0049, 0x0045, 0x0048, 0x00d0, 0x009d, 0x00b9, 0x0046, 0x00cf, 0x009c, 0x00ae, 0x009d, 0x009c, 0x00a0},
        {0x00b7, 0x00ba, 0x00a2, 0x00cf, 0x00b8, 0x00d2, 0x00a8, 0x004a, 0x0025, 0x0036, 0x00c6, 0x0023, 0x00cb, 0x009a, 0x00b8, 0x0099, 0x0043, 0x0035, 0x00b8, 0x00ac, 0x0031, 0x003b, 0x0016, 0x0038, 0x00c7, 0x0051, 0x00cb, 0x00b8, 0x00af, 0x00cd, 0x002d, 0x0032},
        {0x00c5, 0x00b2, 0x00aa, 0x00c0, 0x00d0, 0x0017, 0x00a9, 0x00c9, 0x0045, 0x00c2, 0x00be, 0x0031, 0x00a9, 0x00bc, 0x0031, 0x00c1, 0x00b8, 0x009f, 0x00cb, 0x004e, 0x009f, 0x0015, 0x0047, 0x004f, 0x0047, 0x001f, 0x001d, 0x0029, 0x0047, 0x00a7, 0x009c, 0x00bd},
        {0x00c1, 0x0022, 0x009e, 0x00d0, 0x00ab, 0x00c1, 0x0092, 0x00c7, 0x00d0, 0x002c, 0x0048, 0x0014, 0x00a8, 0x004e, 0x004f, 0x00bd, 0x00cb, 0x003f, 0x0030, 0x0036, 0x0098, 0x00d2, 0x002a, 0x00c6, 0x009d, 0x00c2, 0x004a, 0x0046, 0x003f, 0x00c5, 0x00a2, 0x00a9}
};

static uint16_t wt_algn_fp8e5[32][33] = {
        {0xfb, 0x00, 0x00, 0xfe, 0xe0, 0xff, 0xf8, 0xff, 0xc8, 0x00, 0x00, 0x30, 0xfa, 0x00, 0xff, 0x00, 0x00, 0xff, 0x90, 0x00, 0x20, 0x00, 0xff, 0x00, 0x00, 0x40, 0x00, 0xfc, 0x00, 0x01, 0x38, 0xfc, 0x05},
        {0xff, 0x00, 0x00, 0x60, 0xff, 0xd0, 0xb0, 0xf4, 0x00, 0x05, 0xf9, 0x00, 0xff, 0xfb, 0xff, 0xf8, 0xfe, 0x00, 0xf2, 0xfc, 0xff, 0x01, 0x00, 0xff, 0xff, 0x50, 0x05, 0x0e, 0x18, 0x00, 0xf9, 0x00, 0x05},
        {0xff, 0x00, 0x18, 0x00, 0x00, 0xfb, 0x10, 0xff, 0x01, 0x00, 0xa0, 0xff, 0xff, 0x00, 0x00, 0x00, 0xe4, 0x28, 0x00, 0x00, 0xff, 0x18, 0xfc, 0x00, 0x01, 0x0c, 0x00, 0xff, 0xff, 0x00, 0x03, 0x01, 0x05},
        {0x00, 0x18, 0x00, 0x0c, 0xff, 0xff, 0x00, 0x20, 0x90, 0xf6, 0x1c, 0x00, 0x08, 0xf2, 0x06, 0xff, 0xfc, 0xf6, 0xff, 0xff, 0x00, 0x00, 0x28, 0x00, 0xff, 0x40, 0xf8, 0x00, 0x00, 0xf9, 0xff, 0xe0, 0x05},
        {0xe0, 0x18, 0x1c, 0x28, 0xff, 0xe4, 0xe4, 0x00, 0xff, 0xff, 0xfe, 0x01, 0xe0, 0xff, 0xfd, 0xa0, 0x00, 0x0c, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0xff, 0x06, 0x00, 0xf6, 0xfe, 0x05},
        {0x00, 0xff, 0xff, 0xff, 0x00, 0x00, 0xff, 0x00, 0xff, 0xff, 0x06, 0x70, 0xff, 0xff, 0x00, 0x00, 0xff, 0x00, 0xff, 0x08, 0xff, 0xff, 0xff, 0x00, 0xff, 0x00, 0xff, 0xfe, 0xff, 0x00, 0xff, 0xff, 0x04},
        {0xd0, 0xff, 0xff, 0x0a, 0xff, 0x60, 0x90, 0x00, 0x0c, 0xff, 0x30, 0xfd, 0x18, 0xff, 0x00, 0xff, 0xff, 0x00, 0xf6, 0x00, 0xff, 0x00, 0xff, 0xff, 0xfb, 0xf2, 0x03, 0xff, 0xff, 0xff, 0x07, 0xff, 0x05},
        {0x00, 0xfc, 0x90, 0xff, 0x00, 0x00, 0xff, 0xff, 0x03, 0xff, 0x00, 0x00, 0xff, 0xff, 0x00, 0xff, 0xf8, 0x00, 0xff, 0xff, 0x30, 0xff, 0xff, 0x01, 0x00, 0x00, 0xff, 0xfe, 0x00, 0x00, 0xff, 0xfd, 0x05},
        {0x00, 0x00, 0xfb, 0x02, 0x00, 0xff, 0x08, 0xff, 0x00, 0x02, 0xc8, 0x04, 0x00, 0xfe, 0x0a, 0xd8, 0xff, 0x01, 0x00, 0x00, 0x14, 0x00, 0x07, 0xe8, 0x01, 0xff, 0x00, 0xff, 0xff, 0xc0, 0x0c, 0x00, 0x05},
        {0x0c, 0xff, 0xa0, 0xff, 0x00, 0x00, 0xc8, 0x08, 0xe4, 0xfa, 0xff, 0xff, 0x00, 0x0a, 0x00, 0x00, 0x00, 0xf0, 0xff, 0xff, 0x00, 0x00, 0x02, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0x03, 0x00, 0xff, 0x05},
        {0x14, 0x00, 0xff, 0x00, 0x00, 0x00, 0x60, 0xff, 0xa0, 0x02, 0xff, 0x00, 0x00, 0xff, 0x00, 0xff, 0x0a, 0xff, 0xff, 0xff, 0x00, 0x1c, 0x70, 0x00, 0xff, 0xc0, 0x00, 0xfe, 0xfe, 0x00, 0x01, 0x03, 0x05},
        {0xff, 0xff, 0xb0, 0xf8, 0x04, 0x00, 0xff, 0xff, 0x01, 0xf4, 0xff, 0x1c, 0x02, 0xff, 0x00, 0xfd, 0x02, 0x01, 0x00, 0x00, 0x00, 0xfe, 0x00, 0xf9, 0x00, 0xfe, 0xff, 0x00, 0xfe, 0x00, 0xfb, 0x05, 0x05},
        {0x30, 0x00, 0xff, 0x00, 0x30, 0x00, 0x00, 0x00, 0xff, 0xfe, 0x06, 0x00, 0xff, 0x01, 0x00, 0xff, 0x90, 0xff, 0x07, 0x01, 0x06, 0x05, 0x00, 0x00, 0xff, 0x00, 0xd0, 0x00, 0x00, 0xff, 0x00, 0x04, 0x05},
        {0xff, 0x90, 0xfe, 0xff, 0xff, 0xfe, 0xff, 0xff, 0x00, 0x00, 0xfd, 0xff, 0xff, 0x00, 0xff, 0x28, 0xff, 0xf8, 0xff, 0xff, 0x1c, 0x00, 0x00, 0xb0, 0xff, 0x03, 0x01, 0xd0, 0xfb, 0xd0, 0x00, 0xe8, 0x05},
        {0xf6, 0x00, 0xff, 0x03, 0xa0, 0x01, 0xf9, 0xfe, 0xfe, 0x00, 0xe4, 0x0c, 0xf8, 0x00, 0xfb, 0xfd, 0x07, 0x0e, 0xff, 0x00, 0x00, 0xf2, 0x50, 0xfc, 0xff, 0x70, 0xff, 0x00, 0xd8, 0x00, 0x00, 0x01, 0x04},
        {0x1c, 0xff, 0xfc, 0xf9, 0xfd, 0xfe, 0x14, 0xf2, 0x20, 0xff, 0x05, 0x00, 0x00, 0xff, 0xff, 0xff, 0xfb, 0x00, 0xff, 0xe4, 0xff, 0x00, 0x00, 0xfa, 0x50, 0x00, 0xff, 0xec, 0xff, 0xff, 0xff, 0x00, 0x04},
        {0xff, 0xd8, 0x00, 0xf2, 0xff, 0x00, 0x28, 0x0e, 0xfd, 0x05, 0x18, 0xb0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x04, 0xfe, 0xfe, 0xe4, 0xff, 0x00, 0x30, 0xff, 0x00, 0x28, 0x00, 0xff, 0xff, 0x05},
        {0xff, 0x06, 0xf0, 0xfd, 0xc8, 0xff, 0x18, 0x00, 0xec, 0x00, 0x50, 0xff, 0x06, 0x00, 0xff, 0xb0, 0xff, 0x00, 0x03, 0xfe, 0x18, 0x00, 0xd0, 0x00, 0x03, 0x00, 0xff, 0x00, 0xff, 0x00, 0xfe, 0xff, 0x05},
        {0xff, 0x01, 0x00, 0xff, 0xff, 0x0a, 0x02, 0x00, 0xff, 0x00, 0x30, 0x70, 0x03, 0xff, 0x04, 0x70, 0xff, 0xfd, 0x20, 0x00, 0x02, 0x00, 0x14, 0x08, 0x00, 0xa0, 0x00, 0xfb, 0xff, 0x00, 0x00, 0x00, 0x05},
        {0xff, 0x14, 0xff, 0xb0, 0xec, 0xfc, 0xec, 0x00, 0xfc, 0xd8, 0xfc, 0x30, 0x0e, 0xff, 0x00, 0xd0, 0xd8, 0xf2, 0x00, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xf4, 0x00, 0x00, 0x00, 0xec, 0x00, 0x00, 0x00, 0x05},
        {0xff, 0xff, 0xfe, 0xff, 0x01, 0xff, 0xff, 0x00, 0x02, 0xd8, 0xff, 0xff, 0xf2, 0xff, 0xf8, 0x00, 0x00, 0x0a, 0xff, 0xa0, 0xff, 0x00, 0xff, 0xfd, 0xff, 0x38, 0x00, 0xff, 0x00, 0xfc, 0x00, 0xfe, 0x05},
        {0xff, 0xff, 0xf9, 0x00, 0xff, 0x01, 0x08, 0x03, 0x00, 0x00, 0x00, 0xff, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x50, 0x00, 0x10, 0x00, 0xff, 0xff, 0xd8, 0x00, 0x20, 0x00, 0xff, 0xc8, 0xff, 0xff, 0x05},
        {0xff, 0x00, 0x00, 0xd8, 0x00, 0xfa, 0xff, 0x00, 0x00, 0x06, 0x00, 0xff, 0x00, 0x00, 0xe0, 0x1c, 0x03, 0xff, 0xd8, 0x00, 0x40, 0x03, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0xff, 0x00, 0x00, 0xff, 0x04},
        {0xfe, 0xc8, 0x0a, 0xfc, 0x00, 0xf2, 0x03, 0xff, 0x00, 0xfe, 0x00, 0x00, 0x00, 0xff, 0x00, 0xfe, 0xf8, 0xff, 0x03, 0x14, 0xff, 0x00, 0xff, 0xff, 0x40, 0x70, 0x00, 0xff, 0x00, 0x40, 0x02, 0x00, 0x05},
        {0x0e, 0x40, 0xff, 0xf0, 0xff, 0x02, 0xc8, 0x90, 0x00, 0xff, 0x00, 0x00, 0xc8, 0x00, 0x00, 0x00, 0x08, 0x00, 0xff, 0x90, 0x00, 0x00, 0x00, 0xff, 0x03, 0xff, 0x00, 0xf6, 0x00, 0xe0, 0xff, 0xff, 0x04},
        {0xff, 0x00, 0xe4, 0x00, 0x00, 0xfd, 0xff, 0x00, 0x00, 0x00, 0x0c, 0xfe, 0xff, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0x01, 0x00, 0xe4, 0xfe, 0x06, 0x00, 0x00, 0x00, 0x70, 0xff, 0x00, 0x02, 0x03, 0x05},
        {0xff, 0xff, 0x60, 0x00, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x02, 0x00, 0x28, 0xec, 0x38, 0x01, 0xff, 0xff, 0x00, 0xff, 0x40, 0xff, 0x00, 0xff, 0x00, 0xfe, 0xff, 0xff, 0x00, 0x00, 0xff, 0xfe, 0xf2, 0x05},
        {0x00, 0x00, 0x00, 0x08, 0xff, 0xf4, 0xff, 0xf6, 0xff, 0x02, 0xfe, 0x0a, 0xfc, 0xff, 0x00, 0x0e, 0x01, 0xd8, 0xc8, 0x08, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xb0, 0x1c, 0x01, 0xd8, 0xa0, 0x00, 0x03},
        {0x00, 0x02, 0xff, 0xff, 0x01, 0xff, 0xf2, 0x01, 0x18, 0x01, 0xff, 0x01, 0x00, 0x01, 0x0a, 0xc8, 0x00, 0x02, 0x00, 0x14, 0x0a, 0x10, 0xc0, 0xff, 0xfe, 0x0c, 0xc8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x05},
        {0xff, 0xfe, 0xff, 0xc8, 0xff, 0xa0, 0xff, 0x18, 0x00, 0x00, 0xf4, 0x00, 0xe4, 0xff, 0xff, 0xff, 0x07, 0x00, 0xff, 0xff, 0x00, 0x01, 0x00, 0x01, 0xf2, 0x50, 0xe4, 0xff, 0xff, 0xd8, 0x00, 0x00, 0x05},
        {0xf6, 0xff, 0xff, 0xfc, 0xc0, 0x00, 0xff, 0xec, 0x0a, 0xfa, 0xfd, 0x00, 0xff, 0xfe, 0x00, 0xfb, 0xff, 0xff, 0xe4, 0x30, 0xff, 0x00, 0x0e, 0x38, 0x0e, 0x00, 0x00, 0x00, 0x0e, 0xff, 0xff, 0xfd, 0x05},
        {0xfb, 0x00, 0xff, 0xc0, 0xff, 0xfb, 0x00, 0xf2, 0xc0, 0x00, 0x10, 0x00, 0xff, 0x30, 0x38, 0xfd, 0xe4, 0x03, 0x00, 0x00, 0xff, 0xa0, 0x00, 0xf4, 0xff, 0xfa, 0x18, 0x0c, 0x03, 0xf6, 0xff, 0xff, 0x05}
};



// INT16
static uint16_t wt_algn_int16[16][33] = {
        {0xb410,0x36e5,0x13aa,0x33ad,0xd46d,0x01e0,0x194c,0x6b8a,0x3f21,0x2510,0x2408,0x3e50,0xba2f,0x9e78,0xc34a,0x9261,0xf5ad,0x617d,0x15ff,0x8d52,0xaefb,0x80a2,0xa910,0x67bb,0x93c5,0xa404,0xba6a,0xb358,0xc9f0,0x75df,0x1e30,0x7c00,0x0000},
        {0xbe63,0x3cbb,0x00b1,0xa33b,0x4812,0x8f03,0x1e4d,0x6685,0xe4ae,0x660c,0xc21d,0x0608,0x2544,0x51f2,0x733b,0xd534,0x1cca,0x7723,0x491f,0xabc2,0x32a0,0xfed4,0xd6b8,0xce82,0x6e40,0xa912,0x99eb,0x638e,0x54b7,0x6066,0xe737,0x7766,0x0000},
        {0x4d6b,0x87a8,0xf4e2,0x2e73,0xd98b,0x828d,0x0a67,0xc3e1,0xd8af,0xeb74,0xd316,0xbcdf,0xda58,0x7278,0x5236,0x8f1f,0xe876,0xe75c,0x6944,0xc3bf,0x6f7b,0x1117,0x82a4,0x4a24,0xf42d,0xe933,0x7e3c,0x93c8,0xe603,0x4cd6,0xbd1d,0xac3d,0x0000},
        {0x3af0,0xf457,0x0087,0x0dbf,0x1e60,0x4b93,0x515a,0x5a8c,0xf8bd,0x5a64,0x02a3,0xd637,0x491e,0xa646,0xed09,0x872b,0x44b6,0xe7a3,0x0f87,0x5b23,0x8817,0xbca4,0x26a2,0x2243,0x92a8,0x44e8,0xd5f9,0x6af9,0x9825,0xf0fb,0x977e,0xd37f,0x0000},
        {0x9f1f,0x26fc,0xc25a,0xc206,0xe55c,0x25ec,0x7ab5,0xb88d,0x4699,0x1074,0xdc97,0x1fa3,0xb679,0xd7d6,0xe395,0x34af,0xf1f0,0xc073,0x2251,0x1697,0x2927,0xceec,0xbee3,0x9f01,0x3292,0x37e0,0xe234,0xbcde,0x2e64,0xf06e,0xa86b,0x6bd2,0x0000},
        {0xc210,0xa0d7,0x0e74,0xa0c7,0x3817,0x4811,0xc5f2,0xbd07,0x321f,0x3668,0x34f0,0x5977,0x18e2,0x45b3,0xfff9,0x2c26,0xe547,0x3f7a,0xeaa3,0x4d7d,0x5ff1,0x55e5,0xb40f,0xdd2f,0x8973,0xce7d,0x07bd,0xf017,0x054f,0xe917,0xd316,0x0cd8,0x0000},
        {0x6efe,0x25cf,0xe8a0,0x69b0,0xd442,0x1110,0xd74b,0xe5c4,0x74d0,0x7691,0xe2f5,0x7d90,0x43dc,0x46cb,0xca64,0x6a91,0x5436,0xef70,0x2eaf,0x259b,0xa629,0x20e8,0x0b9b,0xe676,0x2faa,0x565f,0x3799,0x87b4,0x916a,0xe192,0xe274,0x9f97,0x0000},
        {0x45ab,0x6e73,0x29fe,0x8506,0xd6af,0x1cb8,0xc33d,0xd83b,0x7f62,0xb333,0x7cbf,0x8e30,0xd0c2,0x3301,0xe652,0x8422,0x14f5,0xf290,0xb7fa,0x6b82,0x3a2b,0xb995,0xbbd5,0x2abd,0x91c5,0xa818,0x8678,0x5095,0x788c,0xa771,0xbdb1,0xb511,0x0000},
        {0x1672,0x5e11,0x20f9,0x251d,0xbbc9,0x70a4,0x833f,0x85a1,0xd341,0x7ac8,0x8aa4,0xe6b3,0x954d,0xb2a4,0x4bcd,0xd558,0xa9d9,0xbfd7,0x2ab6,0x28d2,0x988e,0xf6f6,0xea54,0x4165,0xcfa5,0x00b7,0x9dbd,0xe9e0,0xcd4c,0xc085,0x5e37,0x4598,0x0000},
        {0x2100,0x0349,0xef9b,0xe3a8,0xee47,0xf4c2,0xd68d,0xb4c1,0xe32e,0x445a,0x5af4,0x1e3a,0x84b6,0x2cec,0xfff3,0x1c3c,0x0ccb,0x5b17,0x4b24,0x71fa,0x7a69,0x9582,0xd5ec,0x1815,0xed50,0x98d7,0x030f,0xf967,0x3c67,0x3e75,0x2433,0xa760,0x0000},
        {0xe49f,0xd92d,0xd6d8,0xfad9,0x1c7e,0x77e1,0x08ba,0xa314,0xf3df,0xd00c,0x0a44,0x6993,0x36e0,0xec4c,0xb645,0x87b7,0xc72b,0x5a07,0xf65a,0xe263,0x57a9,0x373c,0x658e,0xd766,0x7285,0x01fc,0x19c7,0x935a,0xc5cc,0xb5e6,0x19fe,0x0528,0x0000},
        {0xc5ca,0x3ee2,0xbc68,0x3400,0xcaa1,0xe017,0x4846,0xc580,0x86f5,0xdd31,0xeb3b,0x19a6,0xa1b2,0xcd22,0x1115,0xd3c9,0x7506,0xccb9,0x1106,0xa462,0xd91a,0x2bed,0xd4ab,0x13b0,0x8adf,0xe27e,0x4aa5,0x1c4d,0xde7f,0xee84,0x23a3,0x9b33,0x0000},
        {0x9b53,0x8858,0x5d52,0x5a7a,0x5ff2,0x8d89,0x1410,0x3270,0xa526,0xae55,0x7251,0x42bc,0x1fab,0xf373,0x2d44,0xa99e,0x05fa,0x11f1,0x6c8d,0x8b3a,0x650b,0x6b37,0x321c,0xc16e,0x7e5b,0xda3c,0xa576,0xbec3,0x161d,0x50c5,0x3c2c,0x7a4a,0x0000},
        {0x46d7,0xe5f4,0x5839,0x8c5e,0x7801,0xd7b5,0xd6aa,0xa10d,0xbb80,0x75d8,0xd533,0x8520,0x0b30,0xebe4,0x4bf4,0x004e,0x92b9,0x8ba3,0x95cd,0x6429,0x1326,0x8f6d,0x353b,0x9289,0x36bb,0xcc74,0x1d35,0x7d39,0x00d5,0xdf72,0x926c,0x19f8,0x0000},
        {0x8a3b,0x3c3c,0x2aff,0x4955,0x0bc4,0x65ae,0xfc6d,0x9b85,0x4922,0x163c,0x8b43,0x2aaa,0x23f8,0x0011,0x5088,0x75b5,0x19b2,0x9c80,0x2099,0x1e56,0x6ef4,0x5845,0x1c70,0xc96d,0x4744,0xf401,0x3205,0xdd1b,0x489f,0x8af1,0xfbd2,0x3b36,0x0000},
        {0x992e,0xfa48,0xeedf,0x5653,0x363c,0x847d,0xec97,0x0f7a,0x4e98,0xb94b,0x4a52,0x7ae7,0x7ad7,0xa3fa,0x42ca,0x46ac,0x13d1,0x75fa,0xe997,0xbfe6,0x9a18,0x25b2,0xcf55,0xfb32,0x254c,0x3e78,0x5bd0,0x77a9,0x8ba2,0x4ee6,0x4a1a,0xd27e,0x0000}
};

// INT8
static uint16_t wt_algn_int8[32][33] = {
        {0x0084,0x0040,0x00e6,0x00d0,0x0006,0x00c1,0x0090,0x007b,0x007a,0x0008,0x0036,0x00e9,0x005b,0x0041,0x005f,0x00e4,0x00dc,0x0053,0x00cc,0x0027,0x00eb,0x0006,0x00f4,0x0050,0x003d,0x0043,0x00f9,0x0013,0x00ec,0x005c,0x00f8,0x00ef,0x0000},
        {0x004a,0x00cc,0x0051,0x00e9,0x0009,0x00db,0x00af,0x0057,0x00fc,0x00b1,0x002a,0x003f,0x002c,0x00f2,0x00c1,0x0076,0x0069,0x00ef,0x00c9,0x006c,0x0005,0x004d,0x00ab,0x0079,0x008b,0x0021,0x005c,0x00f6,0x0083,0x00eb,0x003a,0x004b,0x0000},
        {0x007a,0x00d2,0x00a1,0x003c,0x00e4,0x00c2,0x0081,0x0024,0x00f3,0x007d,0x00e6,0x00ed,0x00df,0x0003,0x0026,0x004a,0x006d,0x00c1,0x001e,0x000f,0x00c0,0x00c9,0x0052,0x0042,0x001e,0x00b9,0x00fd,0x002d,0x002c,0x0038,0x00d5,0x0031,0x0000},
        {0x0014,0x0068,0x00f8,0x003a,0x0070,0x008f,0x00a1,0x00ee,0x008e,0x0007,0x0073,0x0026,0x003f,0x008a,0x0082,0x0011,0x003c,0x00c6,0x006b,0x009b,0x00ea,0x006a,0x00cf,0x000c,0x0028,0x001d,0x00f9,0x00cd,0x001d,0x00a5,0x0069,0x00ab,0x0000},
        {0x0000,0x0018,0x0075,0x0067,0x00ff,0x00c0,0x0050,0x00a9,0x00d2,0x0062,0x0025,0x0079,0x0027,0x003c,0x00cf,0x0073,0x0005,0x0089,0x0073,0x0053,0x0072,0x008e,0x002f,0x00e9,0x0095,0x007f,0x0097,0x0093,0x0034,0x00fb,0x0080,0x009b,0x0000},
        {0x008b,0x00a2,0x00e6,0x009b,0x00ea,0x00a3,0x0009,0x0014,0x0082,0x00d6,0x0025,0x009f,0x0060,0x00fa,0x00ca,0x008e,0x00d8,0x0089,0x00dc,0x0051,0x0019,0x0028,0x004f,0x0014,0x0081,0x00a3,0x009c,0x0089,0x007d,0x00d7,0x007b,0x00c3,0x0000},
        {0x006a,0x00bb,0x0025,0x00e5,0x0002,0x0058,0x0076,0x0068,0x0035,0x007e,0x004a,0x0022,0x00c9,0x00e1,0x00e2,0x00b1,0x001c,0x0079,0x0068,0x007d,0x0090,0x0073,0x00fc,0x00c7,0x0067,0x00fa,0x0020,0x00f7,0x0044,0x00ee,0x007d,0x00c4,0x0000},
        {0x0051,0x0088,0x0025,0x003d,0x00bd,0x000e,0x00e0,0x0085,0x00a9,0x00db,0x0095,0x0033,0x004b,0x00b5,0x0067,0x0077,0x008d,0x00b6,0x005c,0x0072,0x008b,0x003e,0x004c,0x00e6,0x00bc,0x00ca,0x0088,0x00c8,0x0029,0x0091,0x0016,0x00ee,0x0000},
        {0x009c,0x00cc,0x0011,0x0016,0x00a2,0x00b6,0x00c6,0x00d5,0x006c,0x008b,0x00d7,0x002e,0x00c4,0x00f2,0x005c,0x0018,0x00f8,0x0034,0x00f6,0x0041,0x00b5,0x00e0,0x0013,0x0073,0x00d5,0x00fd,0x005b,0x004f,0x00c4,0x0035,0x006f,0x00a4,0x0000},
        {0x0050,0x00fa,0x00e5,0x00a7,0x00e6,0x0027,0x00df,0x0038,0x0042,0x0056,0x0037,0x0001,0x0016,0x0057,0x003d,0x0009,0x004c,0x00a4,0x00a8,0x00ec,0x00bf,0x00c7,0x002c,0x00e4,0x0067,0x00bd,0x0036,0x0070,0x005f,0x003c,0x004f,0x0054,0x0000},
        {0x0065,0x00ea,0x00c5,0x0057,0x0020,0x00ea,0x00bd,0x003a,0x00c2,0x0041,0x004f,0x00d0,0x003c,0x004b,0x0010,0x00d0,0x00c0,0x0092,0x00a0,0x00cb,0x00bc,0x00cc,0x00e2,0x003e,0x0076,0x0078,0x00d1,0x005a,0x00fc,0x00a5,0x00e3,0x003c,0x0000},
        {0x0064,0x00f5,0x00b2,0x00d2,0x0041,0x00b5,0x0070,0x00f0,0x0087,0x001f,0x0055,0x0007,0x008a,0x005b,0x00e7,0x00d5,0x007c,0x00c2,0x005f,0x0065,0x001a,0x00fa,0x0021,0x002c,0x005c,0x00c7,0x0061,0x00af,0x001f,0x002e,0x0044,0x0013,0x0000},
        {0x0070,0x0043,0x0039,0x0002,0x009b,0x004f,0x00b9,0x0016,0x0001,0x0056,0x0085,0x0042,0x00d6,0x00ce,0x0093,0x003b,0x00b9,0x0038,0x0010,0x00ea,0x0088,0x002c,0x002c,0x00c6,0x0053,0x001e,0x0013,0x0055,0x007f,0x0060,0x00e3,0x0084,0x0000},
        {0x00c5,0x0004,0x0015,0x00fd,0x00ca,0x00cd,0x0071,0x0069,0x00ba,0x0090,0x005d,0x008e,0x0058,0x0003,0x00a9,0x00bf,0x0043,0x00bf,0x00ad,0x004e,0x0069,0x0069,0x0047,0x00d4,0x0066,0x00a7,0x008e,0x0071,0x004f,0x002f,0x00cb,0x0010,0x0000},
        {0x00a9,0x0072,0x0034,0x0098,0x00e7,0x00f5,0x002f,0x00e7,0x00ae,0x0092,0x0050,0x0097,0x00e7,0x0022,0x0093,0x00b8,0x0069,0x0087,0x00a3,0x0091,0x0034,0x00d5,0x001c,0x002f,0x00ac,0x0041,0x004e,0x00be,0x00c6,0x0053,0x0086,0x0023,0x0000},
        {0x0035,0x00c6,0x0096,0x009c,0x00f7,0x00c9,0x00a1,0x00f1,0x000b,0x002f,0x0062,0x0063,0x00a4,0x0037,0x0040,0x00fd,0x00cb,0x00b1,0x0080,0x0017,0x001e,0x0055,0x00a2,0x00da,0x004f,0x007a,0x0019,0x00a4,0x0038,0x0030,0x0072,0x00af,0x0000},
        {0x00ae,0x00df,0x004e,0x0066,0x009b,0x0059,0x00a2,0x00d9,0x008d,0x0019,0x00b2,0x005b,0x00d7,0x0036,0x00cd,0x00a1,0x008d,0x0094,0x00ab,0x007e,0x00c2,0x00f5,0x0056,0x008a,0x0084,0x008c,0x00c0,0x0097,0x00ae,0x0011,0x005c,0x0037,0x0000},
        {0x00a1,0x0032,0x00c0,0x00b3,0x0010,0x0001,0x00d5,0x004d,0x0081,0x000d,0x00d7,0x0031,0x00c1,0x003b,0x0038,0x00a6,0x00f3,0x001c,0x001b,0x00d0,0x00f0,0x00fb,0x0033,0x0046,0x0092,0x006f,0x0086,0x004b,0x00e6,0x0053,0x002c,0x0005,0x0000},
        {0x0080,0x00ba,0x0097,0x00c9,0x008c,0x0052,0x0037,0x00d9,0x0038,0x0066,0x00ae,0x0014,0x0073,0x00f5,0x00f7,0x00a2,0x00e5,0x00b6,0x0058,0x005e,0x0060,0x00e7,0x0013,0x00d9,0x00c2,0x007c,0x00f5,0x008d,0x0017,0x0094,0x0061,0x00ae,0x0000},
        {0x0009,0x00ef,0x00e9,0x00ce,0x00fd,0x006c,0x0068,0x0053,0x0063,0x00a7,0x0044,0x00b0,0x00fa,0x00c0,0x0009,0x00a5,0x002e,0x0006,0x00c8,0x00f8,0x00e1,0x0034,0x00e7,0x00b6,0x00eb,0x0040,0x00a2,0x00be,0x00fc,0x00f9,0x0009,0x0010,0x0000},
        {0x007d,0x0096,0x0028,0x0066,0x0058,0x00ee,0x00f6,0x00eb,0x0044,0x0011,0x0050,0x003f,0x00dc,0x00c4,0x00d6,0x009b,0x0025,0x0021,0x0099,0x00a5,0x00c8,0x006d,0x0097,0x00d3,0x00e6,0x003a,0x000b,0x0081,0x0080,0x00fc,0x0058,0x0041,0x0000},
        {0x003c,0x00dd,0x0012,0x004e,0x00b6,0x0070,0x001e,0x0068,0x0016,0x0079,0x00ad,0x00e1,0x00d6,0x008c,0x0046,0x00d9,0x005f,0x0029,0x00a0,0x0061,0x00a3,0x00c3,0x0092,0x002b,0x00f6,0x0023,0x0023,0x0060,0x00f9,0x00f7,0x00cf,0x00ea,0x0000},
        {0x0005,0x009d,0x00e9,0x00ea,0x00e0,0x003e,0x0017,0x008a,0x0027,0x000b,0x00d7,0x005b,0x00c0,0x001d,0x00a3,0x0029,0x0012,0x00f1,0x001a,0x00e6,0x0007,0x0068,0x00fa,0x00bd,0x0084,0x0075,0x0089,0x00aa,0x00cb,0x0064,0x000c,0x000e,0x0000},
        {0x00b8,0x0097,0x002b,0x0073,0x0063,0x003b,0x0065,0x00b4,0x0069,0x00aa,0x0030,0x004b,0x004d,0x0079,0x0080,0x003d,0x002d,0x005a,0x007d,0x0069,0x00a0,0x00ff,0x0087,0x006c,0x00d2,0x00df,0x00d1,0x0054,0x0071,0x002c,0x0090,0x00ae,0x0000},
        {0x008e,0x004a,0x00bc,0x004e,0x005c,0x00f0,0x0068,0x00ac,0x000e,0x0005,0x003e,0x00bc,0x00cb,0x0083,0x004f,0x002e,0x006a,0x00cc,0x00a2,0x00ec,0x005d,0x00f1,0x00c2,0x00e6,0x0086,0x0061,0x0095,0x004b,0x00c7,0x001b,0x00b8,0x0027,0x0000},
        {0x002e,0x008f,0x00d1,0x0026,0x0058,0x006f,0x0078,0x00f1,0x0099,0x001c,0x009d,0x009d,0x00f1,0x0095,0x004c,0x00d5,0x007a,0x0090,0x0064,0x0077,0x00cd,0x00d3,0x00b3,0x0066,0x0050,0x00f9,0x00eb,0x0076,0x00b1,0x001c,0x0015,0x00a7,0x0000},
        {0x0012,0x00aa,0x0064,0x0088,0x0049,0x0047,0x007f,0x00d7,0x008a,0x00ec,0x00da,0x0021,0x003a,0x009d,0x001d,0x0011,0x00f9,0x009d,0x00b3,0x0035,0x009e,0x000a,0x00ee,0x0050,0x00b8,0x002c,0x0026,0x003e,0x00d2,0x0035,0x0055,0x0094,0x0000},
        {0x003e,0x0024,0x0047,0x0020,0x0022,0x0018,0x00ef,0x00d7,0x0038,0x0092,0x0032,0x003a,0x0052,0x0046,0x00f0,0x0009,0x004f,0x0003,0x00fd,0x000f,0x0094,0x0084,0x00ab,0x00af,0x005b,0x00a5,0x0093,0x0094,0x00d3,0x00f4,0x006e,0x00a0,0x0000},
        {0x00d5,0x0025,0x00f4,0x0097,0x0055,0x004b,0x00d3,0x004f,0x00eb,0x0076,0x000c,0x00da,0x007f,0x00fb,0x00a7,0x006e,0x0066,0x00aa,0x0083,0x001b,0x00b7,0x00d3,0x00c1,0x006f,0x00e5,0x00e2,0x00fc,0x006f,0x0037,0x0048,0x004a,0x00b2,0x0000},
        {0x0085,0x0091,0x00d7,0x001d,0x00f6,0x0096,0x00f2,0x00f9,0x007b,0x009f,0x0009,0x0051,0x003f,0x0038,0x00e9,0x007a,0x005a,0x00d7,0x00dc,0x00a3,0x009f,0x0001,0x00f0,0x0049,0x004c,0x001c,0x005f,0x009e,0x003f,0x0040,0x00ec,0x00e5,0x0000},
        {0x0036,0x00da,0x00ef,0x00c0,0x0044,0x00c2,0x005e,0x001a,0x000b,0x0086,0x00d8,0x005f,0x00da,0x002c,0x000f,0x00c3,0x0002,0x00a2,0x00a7,0x00cf,0x00a2,0x0071,0x00de,0x0098,0x0067,0x0058,0x000b,0x0048,0x0041,0x00ee,0x0017,0x00ab,0x0000},
        {0x00c2,0x00f9,0x0034,0x0090,0x003f,0x0071,0x0044,0x003d,0x0085,0x0083,0x00ff,0x0015,0x00cc,0x000c,0x0038,0x00f9,0x00c0,0x009c,0x003d,0x001a,0x002f,0x007f,0x00a4,0x00e3,0x001a,0x0011,0x00ef,0x0031,0x0023,0x003b,0x0019,0x008b,0x0000}
};

// INT4
static uint16_t wt_algn_int4[64][33] = {
{0x0005,0x000c,0x0006,0x0001,0x0003,0x000d,0x0007,0x0009,0x0001,0x000f,0x0008,0x0003,0x000f,0x0007,0x0001,0x0001,0x000f,0x000a,0x000d,0x000e,0x000e,0x0000,0x000a,0x000b,0x0006,0x000b,0x0002,0x0007,0x0004,0x000d,0x0003,0x0001,0x0000},
{0x0009,0x000a,0x000a,0x0005,0x000b,0x000c,0x0000,0x0003,0x000d,0x000b,0x000d,0x000d,0x0009,0x0007,0x000a,0x000a,0x0009,0x0008,0x0007,0x0005,0x0001,0x0000,0x0007,0x000b,0x0003,0x0006,0x0003,0x0004,0x0006,0x0008,0x0002,0x000e,0x0000},
{0x0005,0x0008,0x0000,0x0007,0x000d,0x0007,0x000d,0x0008,0x0005,0x0004,0x000b,0x000c,0x000b,0x000c,0x000b,0x0008,0x0009,0x0002,0x000f,0x000c,0x000d,0x000c,0x000e,0x0005,0x0005,0x000d,0x0000,0x0006,0x0003,0x0005,0x0003,0x000b,0x0000},
{0x000d,0x000a,0x0006,0x000b,0x0003,0x0009,0x0008,0x0009,0x000d,0x000b,0x0000,0x0002,0x000c,0x0005,0x0005,0x000e,0x0009,0x000f,0x0003,0x000b,0x0007,0x0001,0x0009,0x0001,0x0006,0x0003,0x0007,0x0002,0x000e,0x0002,0x0000,0x0009,0x0000},
{0x0009,0x000f,0x000a,0x0008,0x0005,0x0009,0x000e,0x000d,0x0004,0x000d,0x000d,0x000f,0x000f,0x0000,0x0007,0x000b,0x0006,0x000e,0x0006,0x000e,0x000d,0x000f,0x0004,0x000b,0x000d,0x0001,0x0002,0x0002,0x000d,0x0006,0x0003,0x0005,0x0000},
{0x0001,0x0001,0x000b,0x000a,0x0003,0x0004,0x0006,0x0000,0x0008,0x0002,0x0003,0x000a,0x000b,0x000c,0x000b,0x000b,0x000e,0x0006,0x000f,0x0001,0x0000,0x000e,0x000e,0x0000,0x000f,0x000c,0x0007,0x0005,0x0009,0x000a,0x000c,0x0001,0x0000},
{0x0007,0x000c,0x000b,0x0003,0x000e,0x0004,0x0005,0x0008,0x000e,0x0007,0x0006,0x0002,0x0004,0x000d,0x0007,0x0007,0x0003,0x0005,0x000e,0x0000,0x000c,0x0002,0x0009,0x0006,0x0005,0x0002,0x0008,0x0005,0x000f,0x000f,0x0000,0x000f,0x0000},
{0x0007,0x0003,0x000d,0x0006,0x0002,0x000f,0x0008,0x0005,0x0002,0x0005,0x0009,0x000e,0x000f,0x000f,0x0008,0x0006,0x0001,0x0000,0x0005,0x000b,0x000a,0x000f,0x000d,0x0005,0x000e,0x0000,0x000d,0x000d,0x0004,0x0000,0x0001,0x000b,0x0000},
{0x0004,0x0006,0x0000,0x0006,0x000d,0x000e,0x000e,0x000b,0x0005,0x0006,0x0002,0x000b,0x0001,0x0001,0x0006,0x0003,0x000d,0x000b,0x0005,0x0004,0x0008,0x000e,0x000d,0x0003,0x000d,0x000c,0x0004,0x0007,0x0001,0x0008,0x000d,0x0001,0x0000},
{0x0006,0x000c,0x0003,0x000b,0x0006,0x000b,0x000f,0x000a,0x0004,0x0002,0x0008,0x000c,0x0009,0x0006,0x0000,0x0009,0x0008,0x0005,0x0001,0x0002,0x0000,0x0004,0x0003,0x0006,0x000d,0x0000,0x000c,0x0008,0x0000,0x000b,0x0000,0x0005,0x0000},
{0x0005,0x0004,0x000c,0x0004,0x0009,0x0006,0x000c,0x000a,0x0004,0x0005,0x0008,0x0008,0x000f,0x0006,0x0006,0x000a,0x0001,0x0004,0x0004,0x0004,0x000c,0x0006,0x000b,0x000c,0x000a,0x0001,0x0004,0x0003,0x0007,0x000c,0x000a,0x000b,0x0000},
{0x0002,0x0003,0x0003,0x0005,0x0002,0x0008,0x000f,0x0000,0x0002,0x0003,0x000f,0x000a,0x000a,0x000e,0x0007,0x0005,0x0000,0x0009,0x000e,0x0008,0x000d,0x0007,0x0001,0x000a,0x0001,0x0008,0x0007,0x000d,0x0005,0x0001,0x0004,0x0004,0x0000},
{0x000a,0x0003,0x000f,0x000b,0x000c,0x000a,0x0004,0x0006,0x000c,0x0003,0x000c,0x000d,0x000e,0x000b,0x000d,0x000f,0x0000,0x000a,0x000b,0x000f,0x0002,0x0007,0x0008,0x0003,0x000c,0x000a,0x0008,0x0001,0x0002,0x000b,0x0009,0x000a,0x0000},
{0x0008,0x0009,0x000a,0x0005,0x0002,0x0009,0x000d,0x0001,0x0005,0x000d,0x0006,0x000a,0x0001,0x0008,0x0000,0x0006,0x000b,0x000d,0x0003,0x0009,0x0000,0x0004,0x0009,0x0005,0x0007,0x0001,0x0004,0x0005,0x000c,0x0001,0x0002,0x0002,0x0000},
{0x0003,0x000f,0x0005,0x0006,0x0001,0x0004,0x000c,0x000d,0x000e,0x0008,0x0007,0x0007,0x0000,0x0006,0x0004,0x0009,0x0002,0x000e,0x000f,0x000f,0x0008,0x000c,0x0008,0x0002,0x0004,0x0009,0x0001,0x0008,0x000c,0x0005,0x0009,0x000d,0x0000},
{0x0009,0x000e,0x0006,0x0005,0x0009,0x0009,0x000b,0x000a,0x000e,0x000a,0x0003,0x0003,0x000b,0x0004,0x0007,0x0009,0x000a,0x0005,0x000f,0x000e,0x0006,0x0001,0x0005,0x0008,0x0002,0x000b,0x000c,0x000d,0x000f,0x000d,0x0006,0x0000,0x0000},
{0x000c,0x0004,0x000d,0x000c,0x0007,0x000f,0x0003,0x0002,0x0009,0x000b,0x000a,0x000d,0x0003,0x0000,0x0005,0x0008,0x0001,0x0005,0x0002,0x000b,0x0006,0x0006,0x000b,0x0005,0x000a,0x000c,0x0003,0x000d,0x0008,0x0008,0x0002,0x0003,0x0000},
{0x0009,0x0006,0x0000,0x000e,0x000d,0x0006,0x0009,0x0007,0x0003,0x000d,0x0004,0x0002,0x0001,0x000c,0x000a,0x0004,0x0009,0x0003,0x0007,0x000f,0x000a,0x0007,0x0002,0x0001,0x000b,0x000c,0x000c,0x000d,0x0008,0x0007,0x0001,0x0005,0x0000},
{0x000e,0x000c,0x000d,0x0005,0x000e,0x000d,0x000b,0x0000,0x0005,0x0009,0x0004,0x000b,0x0002,0x0008,0x000e,0x0007,0x0007,0x0002,0x0009,0x000d,0x000f,0x0006,0x0001,0x0005,0x000b,0x0005,0x000a,0x0002,0x000e,0x000e,0x000b,0x000c,0x0000},
{0x000d,0x000d,0x000d,0x0000,0x000c,0x0008,0x000e,0x000b,0x0004,0x0009,0x000e,0x0001,0x0009,0x000c,0x0000,0x000b,0x0002,0x0008,0x0000,0x000d,0x000d,0x0005,0x000e,0x0003,0x0009,0x000b,0x000a,0x0007,0x0007,0x000f,0x000d,0x0008,0x0000},
{0x0004,0x000f,0x0007,0x000d,0x0002,0x0003,0x0002,0x0003,0x0005,0x000d,0x0008,0x0003,0x0009,0x0002,0x000d,0x0006,0x0007,0x0000,0x000f,0x0006,0x0001,0x0005,0x0004,0x000e,0x000c,0x000a,0x000b,0x0000,0x0005,0x0004,0x000a,0x0006,0x0000},
{0x0003,0x0000,0x000c,0x000a,0x0008,0x0000,0x000f,0x0007,0x000b,0x000d,0x0000,0x0002,0x000f,0x000d,0x000c,0x0009,0x000d,0x0000,0x0005,0x000a,0x0005,0x0000,0x0005,0x000a,0x0000,0x000d,0x000e,0x0008,0x0009,0x000d,0x000a,0x000a,0x0000},
{0x0003,0x0004,0x000e,0x0000,0x000a,0x0005,0x000a,0x0001,0x0003,0x0002,0x0002,0x000b,0x0000,0x000a,0x0005,0x000a,0x0009,0x0008,0x0003,0x0009,0x0000,0x000f,0x0004,0x0001,0x0005,0x0007,0x000e,0x0000,0x0002,0x0005,0x000d,0x0009,0x0000},
{0x0001,0x000d,0x0001,0x0001,0x0002,0x000e,0x0008,0x000b,0x0006,0x000e,0x0000,0x000d,0x000e,0x000a,0x0008,0x0006,0x0002,0x000c,0x000a,0x0007,0x0006,0x000f,0x0004,0x000f,0x0002,0x000b,0x000e,0x0006,0x0009,0x0006,0x000b,0x000a,0x0000},
{0x0006,0x000b,0x000f,0x000f,0x0007,0x000e,0x000e,0x0006,0x0002,0x0003,0x0009,0x0005,0x0002,0x000c,0x0002,0x0002,0x000e,0x0004,0x0002,0x0003,0x000a,0x000e,0x000c,0x0007,0x0000,0x000d,0x000a,0x000b,0x0008,0x000a,0x000b,0x0005,0x0000},
{0x000b,0x000a,0x0006,0x000b,0x0000,0x000d,0x0007,0x0003,0x000d,0x000a,0x0002,0x000c,0x0006,0x0001,0x000a,0x0002,0x000b,0x0007,0x0004,0x0000,0x0005,0x000e,0x0000,0x0003,0x0000,0x0001,0x000f,0x0005,0x000f,0x0006,0x000e,0x000f,0x0000},
{0x0008,0x000d,0x0004,0x000b,0x0006,0x000b,0x000e,0x000c,0x000d,0x000e,0x0003,0x0006,0x0001,0x000e,0x000c,0x000f,0x000d,0x0003,0x000f,0x0003,0x0002,0x0004,0x000b,0x0004,0x000c,0x0008,0x0009,0x0005,0x0001,0x0008,0x0003,0x0007,0x0000},
{0x0001,0x000e,0x000d,0x000d,0x0009,0x0008,0x0000,0x000a,0x0007,0x0008,0x0003,0x0004,0x000b,0x000b,0x000f,0x0006,0x000c,0x000c,0x000a,0x000b,0x0005,0x000a,0x000c,0x000a,0x000c,0x000f,0x0001,0x0002,0x000b,0x0006,0x0009,0x0004,0x0000},
{0x0008,0x0003,0x0008,0x0007,0x0005,0x000f,0x000a,0x000a,0x0002,0x0006,0x000b,0x0001,0x000b,0x0005,0x0002,0x0000,0x0000,0x000b,0x0003,0x000e,0x0005,0x0006,0x0002,0x0004,0x0006,0x0005,0x0008,0x0006,0x000b,0x000f,0x000b,0x000d,0x0000},
{0x0001,0x0007,0x0005,0x000b,0x0005,0x0004,0x0000,0x000a,0x0004,0x000c,0x0008,0x0007,0x0008,0x000a,0x0003,0x000b,0x0008,0x0003,0x000f,0x0003,0x0002,0x0009,0x000f,0x000a,0x000d,0x000c,0x0003,0x0007,0x0002,0x000b,0x0005,0x0007,0x0000},
{0x0006,0x000e,0x0007,0x000b,0x0008,0x0007,0x0006,0x0006,0x0005,0x0004,0x000c,0x000e,0x000a,0x0007,0x0005,0x0003,0x0007,0x0001,0x0004,0x0004,0x0002,0x0007,0x000e,0x000e,0x0002,0x0007,0x000a,0x0005,0x000f,0x000e,0x0006,0x000c,0x0000},
{0x0009,0x000d,0x000b,0x000b,0x0001,0x0002,0x0000,0x0007,0x0002,0x0005,0x000a,0x0003,0x000a,0x000a,0x0008,0x000c,0x000b,0x0002,0x0006,0x000d,0x0000,0x0001,0x0001,0x0007,0x0000,0x000c,0x0006,0x0001,0x0003,0x000e,0x0003,0x000f,0x0000},
{0x0000,0x0003,0x000e,0x0007,0x000f,0x0003,0x0003,0x000b,0x0005,0x0009,0x0003,0x000a,0x0005,0x000f,0x000a,0x000e,0x000e,0x0002,0x000d,0x0002,0x000b,0x000c,0x0004,0x000e,0x0006,0x0003,0x000d,0x000b,0x0005,0x0003,0x0003,0x0003,0x0000},
{0x000d,0x0006,0x0000,0x000f,0x0002,0x000b,0x0008,0x000a,0x000d,0x0004,0x0007,0x0008,0x0004,0x000b,0x0003,0x0007,0x0004,0x0004,0x000e,0x000c,0x0000,0x000b,0x0004,0x0005,0x000c,0x000d,0x000d,0x0004,0x000f,0x0002,0x0006,0x000c,0x0000},
{0x000e,0x0006,0x000c,0x0008,0x0003,0x0003,0x0002,0x0003,0x000b,0x0001,0x000d,0x0009,0x0004,0x0006,0x0002,0x000b,0x0000,0x0001,0x000a,0x0000,0x0001,0x0000,0x0005,0x000d,0x0007,0x000a,0x0003,0x0009,0x0008,0x0007,0x000a,0x0001,0x0000},
{0x000e,0x0008,0x000b,0x0006,0x000a,0x000b,0x000d,0x000b,0x0008,0x000e,0x000f,0x0000,0x000b,0x000d,0x0004,0x0008,0x0000,0x0000,0x000b,0x0002,0x000f,0x000f,0x0004,0x0007,0x0000,0x0006,0x000d,0x0003,0x000d,0x0005,0x0003,0x000e,0x0000},
{0x000c,0x0003,0x0006,0x000a,0x0002,0x000e,0x0002,0x0003,0x0007,0x000e,0x000b,0x000d,0x0003,0x000e,0x0002,0x000e,0x0004,0x0007,0x0001,0x000a,0x0009,0x0004,0x0002,0x0009,0x0006,0x000c,0x000a,0x0003,0x0002,0x0006,0x0007,0x0001,0x0000},
{0x0008,0x0004,0x0001,0x0001,0x0002,0x000c,0x0000,0x0004,0x000c,0x000b,0x000d,0x0005,0x000f,0x000f,0x0005,0x000d,0x0006,0x0004,0x0005,0x0002,0x0003,0x0007,0x000b,0x0006,0x0007,0x0003,0x0009,0x000e,0x000e,0x0002,0x0004,0x000e,0x0000},
{0x0007,0x000e,0x0004,0x000e,0x0009,0x0002,0x0009,0x000b,0x000d,0x000d,0x000f,0x0000,0x0007,0x0007,0x0007,0x0004,0x0006,0x0001,0x0006,0x000e,0x0006,0x0007,0x000e,0x0001,0x000b,0x0006,0x0000,0x000e,0x0009,0x000e,0x000f,0x0002,0x0000},
{0x0009,0x000b,0x0005,0x000b,0x000d,0x000f,0x000f,0x0009,0x0002,0x000c,0x0008,0x0005,0x0002,0x0000,0x0006,0x000e,0x000e,0x000f,0x0009,0x0007,0x0001,0x0004,0x0002,0x000b,0x0005,0x000c,0x0000,0x000c,0x000c,0x000b,0x000b,0x0009,0x0000},
{0x000d,0x0000,0x0005,0x0001,0x000d,0x0009,0x000a,0x0000,0x0009,0x000a,0x0002,0x0006,0x000f,0x0009,0x000e,0x0001,0x0007,0x0009,0x0000,0x0003,0x0008,0x000b,0x000e,0x0009,0x0001,0x0004,0x000a,0x0006,0x0009,0x0000,0x000f,0x0007,0x0000},
{0x0004,0x0004,0x000a,0x0009,0x0006,0x000f,0x0003,0x000b,0x000c,0x000b,0x0002,0x0004,0x0002,0x0002,0x0001,0x000b,0x0002,0x0008,0x000b,0x000c,0x0007,0x000f,0x000d,0x000c,0x0009,0x0006,0x0003,0x000f,0x000d,0x0001,0x000e,0x000e,0x0000},
{0x0004,0x000a,0x0001,0x000c,0x000e,0x0002,0x0008,0x0005,0x0005,0x000c,0x000d,0x0003,0x000a,0x000e,0x0008,0x000d,0x0003,0x0004,0x000d,0x000b,0x0006,0x000a,0x0006,0x0009,0x000a,0x000b,0x0005,0x0001,0x000e,0x0009,0x000c,0x000c,0x0000},
{0x0003,0x000c,0x0003,0x0006,0x000d,0x000d,0x000f,0x000c,0x000a,0x000f,0x000c,0x0003,0x0000,0x000a,0x000c,0x0001,0x0002,0x0008,0x0003,0x0001,0x0003,0x000e,0x000d,0x0000,0x0009,0x000f,0x0008,0x0003,0x000e,0x0000,0x0006,0x0009,0x0000},
{0x000b,0x000d,0x0007,0x0002,0x0002,0x0009,0x0003,0x0003,0x0003,0x0008,0x0007,0x0009,0x0002,0x0008,0x0004,0x0000,0x0002,0x0005,0x000f,0x000a,0x0006,0x0001,0x0001,0x000d,0x0007,0x0001,0x0009,0x0008,0x0003,0x000e,0x0004,0x0006,0x0000},
{0x0005,0x0003,0x000f,0x000f,0x0004,0x000f,0x000b,0x0002,0x0007,0x000d,0x0005,0x0002,0x0006,0x000e,0x000b,0x0007,0x0007,0x000c,0x0001,0x000d,0x000e,0x0001,0x000e,0x0004,0x0006,0x0000,0x000e,0x000e,0x0007,0x0003,0x0004,0x000f,0x0000},
{0x000f,0x0009,0x000f,0x000b,0x0006,0x000d,0x0006,0x000f,0x0001,0x0000,0x0000,0x0009,0x000b,0x0005,0x000d,0x0006,0x0003,0x0000,0x0005,0x000d,0x000c,0x000d,0x000c,0x0004,0x0008,0x000a,0x0002,0x000b,0x000c,0x000e,0x000f,0x0004,0x0000},
{0x0004,0x0000,0x000b,0x000b,0x0007,0x0009,0x000a,0x000f,0x0001,0x0004,0x000e,0x0004,0x000c,0x000d,0x0008,0x0003,0x000f,0x0000,0x0004,0x0005,0x000b,0x0003,0x000d,0x000d,0x000e,0x000f,0x0005,0x000b,0x0007,0x0002,0x000e,0x000d,0x0000},
{0x000c,0x000d,0x000c,0x0007,0x000b,0x0007,0x000f,0x000e,0x000a,0x0000,0x0007,0x0004,0x000b,0x000f,0x0000,0x000d,0x0002,0x0003,0x0004,0x0003,0x0005,0x000f,0x000c,0x0000,0x000b,0x0002,0x0003,0x0008,0x0006,0x0004,0x0007,0x000f,0x0000},
{0x0001,0x0006,0x0004,0x0009,0x0005,0x000b,0x0003,0x0000,0x000f,0x0009,0x000f,0x0000,0x0009,0x000b,0x0000,0x0008,0x0006,0x0009,0x000c,0x0001,0x0005,0x000a,0x000f,0x0000,0x000a,0x000e,0x0009,0x0007,0x0000,0x0008,0x0005,0x0008,0x0000},
{0x000f,0x0008,0x0004,0x000b,0x000a,0x0007,0x000b,0x000c,0x000a,0x0005,0x0009,0x000f,0x000c,0x0000,0x000e,0x0003,0x0006,0x0002,0x000c,0x0001,0x000c,0x0008,0x0009,0x0004,0x0009,0x0008,0x000e,0x0000,0x0001,0x0000,0x000e,0x0001,0x0000},
{0x000d,0x000a,0x0002,0x0000,0x000f,0x0003,0x0002,0x000a,0x0004,0x0000,0x0003,0x000f,0x0006,0x0009,0x000a,0x0002,0x0000,0x0007,0x0003,0x000d,0x0002,0x0002,0x0006,0x0006,0x000a,0x000d,0x000e,0x000e,0x000f,0x0001,0x0001,0x000a,0x0000},
{0x0005,0x0005,0x0001,0x000c,0x0003,0x000e,0x0000,0x0003,0x0002,0x0004,0x0003,0x000f,0x0006,0x0007,0x0000,0x000e,0x0003,0x0008,0x0008,0x0005,0x0007,0x000a,0x000b,0x0001,0x000f,0x000a,0x0005,0x000b,0x0000,0x000d,0x0008,0x0001,0x0000},
{0x000c,0x0003,0x0009,0x000a,0x0001,0x000a,0x0003,0x0007,0x000f,0x0004,0x0005,0x0000,0x0009,0x0009,0x0000,0x000a,0x0001,0x0002,0x0001,0x000e,0x000c,0x0001,0x000c,0x0004,0x0006,0x0001,0x000b,0x0009,0x0006,0x0007,0x0005,0x0001,0x0000},
{0x0000,0x000c,0x0006,0x000c,0x000d,0x0004,0x000e,0x0000,0x0005,0x000a,0x000b,0x000c,0x0007,0x0005,0x000b,0x000a,0x000a,0x0002,0x0002,0x000a,0x000f,0x0003,0x0002,0x0009,0x000b,0x000c,0x0005,0x000b,0x000c,0x0008,0x000d,0x0005,0x0000},
{0x0001,0x000f,0x000f,0x0006,0x000d,0x000b,0x0004,0x000b,0x000f,0x0008,0x000d,0x0003,0x0007,0x0005,0x000b,0x0008,0x0000,0x000a,0x000c,0x000c,0x0008,0x0006,0x0006,0x0008,0x000d,0x0000,0x000f,0x0009,0x0009,0x000d,0x000e,0x0003,0x0000},
{0x0003,0x0000,0x0008,0x000d,0x0008,0x0004,0x0009,0x000c,0x0000,0x000c,0x0008,0x000a,0x000e,0x0007,0x0000,0x000d,0x0004,0x0004,0x0003,0x0009,0x0008,0x0008,0x000d,0x0009,0x0007,0x0005,0x000b,0x000a,0x0005,0x0003,0x0005,0x000a,0x0000},
{0x000a,0x000e,0x0006,0x000d,0x000a,0x0000,0x0008,0x0003,0x0000,0x000e,0x000c,0x000d,0x000f,0x0002,0x0007,0x0002,0x0004,0x0007,0x0007,0x000b,0x0007,0x000f,0x0000,0x000d,0x0003,0x0007,0x000f,0x000f,0x000d,0x000e,0x0007,0x0003,0x0000},
{0x0001,0x0002,0x0001,0x000d,0x0005,0x0005,0x000c,0x000e,0x000d,0x0008,0x0004,0x0007,0x0002,0x0002,0x0008,0x000e,0x000c,0x0001,0x000a,0x000f,0x000c,0x0007,0x000f,0x0001,0x0000,0x0009,0x0003,0x0000,0x0000,0x0006,0x0003,0x0000,0x0000},
{0x0003,0x0004,0x0004,0x0002,0x0003,0x0001,0x0002,0x0003,0x0005,0x000a,0x0001,0x0001,0x0001,0x000f,0x0009,0x0002,0x0002,0x0001,0x000d,0x000e,0x0008,0x0002,0x0006,0x0008,0x0003,0x000f,0x000c,0x000a,0x000e,0x000c,0x0003,0x000b,0x0000},
{0x0008,0x0009,0x000f,0x0008,0x0009,0x0002,0x000c,0x000f,0x0006,0x0006,0x0009,0x000b,0x0006,0x0008,0x000d,0x0000,0x000c,0x000f,0x0000,0x0001,0x000d,0x0006,0x0002,0x0000,0x0009,0x0000,0x0004,0x000b,0x000c,0x000b,0x0001,0x000f,0x0000},
{0x000a,0x0006,0x000c,0x000b,0x000d,0x0008,0x0003,0x000f,0x000e,0x0008,0x000b,0x0004,0x0003,0x000b,0x000c,0x0006,0x0005,0x000a,0x000b,0x0008,0x000d,0x000f,0x0006,0x000b,0x0008,0x0000,0x000f,0x0002,0x0008,0x0003,0x0007,0x0007,0x0000},
{0x0009,0x000f,0x0001,0x0007,0x0004,0x0006,0x000b,0x000b,0x0007,0x0006,0x0000,0x0001,0x0005,0x0000,0x0006,0x0002,0x000e,0x0009,0x000a,0x0006,0x0006,0x0000,0x0001,0x0006,0x000b,0x000f,0x0006,0x0006,0x0005,0x0007,0x0002,0x0008,0x0000},
{0x000c,0x0001,0x0009,0x0002,0x000f,0x000a,0x0007,0x0008,0x0003,0x000c,0x0004,0x0005,0x000d,0x0005,0x0006,0x000c,0x000f,0x0002,0x0006,0x000b,0x0007,0x0001,0x000f,0x000f,0x000f,0x000a,0x000f,0x000b,0x0006,0x0007,0x000a,0x0006,0x0000}
};
