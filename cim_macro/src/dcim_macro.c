#include "dcim_com.h"
#include <stdint.h>





uint32_t dcim_macro_com(uint16_t* wt_algn, uint8_t wt_dtpy, uint16_t* in_algn, uint8_t in_dtpy)
{

    int64_t manti_cmt_s = 0;
    uint8_t manti_cmt_ivt_bst[64] = { 0 };
    uint32_t manti_out = 0;
    uint8_t sign = 0;
    uint8_t bit_cnt = 0;
    uint32_t out_algn = 0;
    uint8_t bit_tmp = 0;
    uint8_t co = 0;
    uint8_t fp32_manti_bst[24] = { 0 };

    uint8_t wtfp_flg, infp_flg = 0;
    uint32_t exp_tmp = 0;
    uint32_t exp_out = 0;
    uint8_t int_bl[4] = { 4, 8, 12, 16 };
    uint8_t algn_bl[10] = { 4, 8, 16, 16, 32, 16, 16, 8, 8, 8 };
    uint8_t sign_cnt[32] = { 0 };

    float out_flt = 0;

    uint8_t cmsb = 20;
    uint8_t tmp = 0;

    // printf("data type: (wt)%d (in)%d \n", wt_dtpy, in_dtpy);

    if (in_dtpy == INT4) {
        for (int i = 0; i < 32; i++) {
            // printf("%04x ->\n", in_algn[i]);
            in_algn[i] = (in_algn[i] & 0xF);
            sign_cnt[i] = ((in_algn[i] >> 3) & 1);
            if (sign_cnt[i])
                in_algn[i] += 0xF0;
        }
        // printf("%hhu %04x \n", sign_cnt[i], in_algn[i]);
    in_dtpy = INT8;
    }


    if (wt_dtpy == INT4) {
        for (int i = 0; i < 32; i++) {
            // printf("%04x -> ", wt_algn[i]);
            wt_algn[i] = (wt_algn[i] & 0xF);
            sign_cnt[i] = ((wt_algn[i] >> 3) & 1);
            if (sign_cnt[i])
                wt_algn[i] += 0xF0;
            // printf("%hhu %04x \n", sign_cnt[i], wt_algn[i]);
        }
        wt_dtpy = INT8;
    }

    manti_cmt_s = 0;
    // if((algn_bl[wt_dtpy] == 16) && (algn_bl[in_dtpy] == 16)) {
    if (algn_bl[wt_dtpy] == 16) {
        // cmsb = 36;
        for (int i = 0; i < 32; i++) {
            if (algn_bl[in_dtpy] == 16) {
                cmsb = 36;
                manti_cmt_s += (int16_t)in_algn[i] * (int16_t)wt_algn[i];
            } else if (algn_bl[in_dtpy] == 8) {
                cmsb = 36 - 8;
                manti_cmt_s += (int8_t)in_algn[i] * (int16_t)wt_algn[i];
            }
        }
    }
    // else if((algn_bl[wt_dtpy] == 8) && (algn_bl[in_dtpy] == 8)) {
    else if (algn_bl[wt_dtpy] == 8) {
        for (int i = 0; i < 32; i++) {
            if (algn_bl[in_dtpy] == 16) {
                cmsb = 20 + 8;
                manti_cmt_s += (int16_t)in_algn[i] * (int8_t)wt_algn[i];
            } else if (algn_bl[in_dtpy] == 8) {
                cmsb = 20;
                manti_cmt_s += (int8_t)in_algn[i] * (int8_t)wt_algn[i];
            }
        }
    }
    //}

// #define DEBUG_EN
#ifdef DEBUG_EN
    printf("manti_cmt_s: %ld\n", manti_cmt_s);
    printf("msb: -");
    for (int i = 0; i < 64; i++) {
        bit_tmp = (manti_cmt_s >> (63 - i)) & 1;
        printf("%d", bit_tmp);
        if ((i & 3) == 3)
            printf("-");
        if ((i & 0x1f) == 0x1f)
            printf("\n      -");
    }
    printf("\n");
#endif

    sign = 0;
    if (manti_cmt_s < 0)
        sign = 1;
    for (int i = 0; i < 64; i++) {
        bit_tmp = (manti_cmt_s >> (63 - i)) & 1;
        manti_cmt_ivt_bst[63 - i] = bit_tmp;
        if (manti_cmt_s < 0) {
            manti_cmt_ivt_bst[63 - i] = ((!(bit_tmp & 1)) & 1);
        }
    }

    if (sign == 1) {
        co = 1;
        for (int i = 0; i < 64; i++) {
            bit_tmp = manti_cmt_ivt_bst[i];
            if (co == 1) {
                co = bit_tmp;
                manti_cmt_ivt_bst[i] = (!bit_tmp) & 1;
            }
        }
    }

    #ifdef DEBUG_EN
    printf("mani_cmt_o_bst -> sign: %d\n", sign);
    printf("msb: -");
    for (int i = 0; i < 64; i++) {
        printf("%d", manti_cmt_ivt_bst[63 - i]);
        if ((i & 3) == 3)
            printf("-");
        if ((i & 0x1f) == 0x1f)
            printf("\n         - ");
    }
    printf("\n");
    #endif

    for (int i = 0; i < cmsb; i++) {
        if (manti_cmt_ivt_bst[cmsb - 1 - i] == 1)
            break;
        bit_cnt++;
    }

    for (int i = 0; i < 24; i++) {
        if (cmsb - 1 - bit_cnt - i >= 0)
            fp32_manti_bst[23 - i] = manti_cmt_ivt_bst[cmsb - 1 - bit_cnt - i];
        else
            fp32_manti_bst[23 - i] = 0;
    }

    #ifdef DEBUG_EN
    printf("bit_cnt: %d\n", bit_cnt);
    printf("manti: ");
    for (int i = 0; i < 24; i++) {
        printf("%d", fp32_manti_bst[23 - i]);
        if ((i & 3) == 3)
            printf("-");
    }
    printf("\n");
    #endif

    manti_out  = 0;
    for (int i = 0; i < 23; i++) {
        manti_out += (fp32_manti_bst[22 - i] << (22 - i));
    }
    // printf("manti_out: 0x%06x\n", manti_out);

    wtfp_flg = (wt_dtpy < 4) ? 0 : 1;
    infp_flg = (in_dtpy < 4) ? 0 : 1;

    if (infp_flg && wtfp_flg) {
        exp_tmp = in_algn[32] + wt_algn[32] + 7;
    } else if (infp_flg && (!wtfp_flg)) {
        exp_tmp = in_algn[32] + int_bl[wt_dtpy] + 5;
    } else if ((!infp_flg) && wtfp_flg) {
        exp_tmp = int_bl[in_dtpy] + wt_algn[32] + 5;
    } else {
        exp_tmp = int_bl[in_dtpy] + int_bl[wt_dtpy] + 3;
    }

    exp_out = (exp_tmp & 0xff) - 1 - bit_cnt + 128; // Fp*FP
    // printf("exp_out: 0x%02x (tmp: %d)\n", exp_out, exp_tmp);

    // if (manti_cmt_s == 0) {
    //     printf("[DEBUG] Zero Sum Detected!\n");
    //     printf("[DEBUG] manti_cmt_s: %ld\n", manti_cmt_s);
    //     printf("[DEBUG] sign: %d\n", sign);
    //     printf("[DEBUG] cmsb: %d\n", cmsb);
    //     printf("[DEBUG] bit_cnt: %d\n", bit_cnt);
    //     printf("[DEBUG] exp_tmp: %u\n", exp_tmp);
    //     printf("[DEBUG] exp_out: %u (0x%02x)\n", exp_out, exp_out);shiyi
    
    //     printf("[DEBUG] manti_out: %u (0x%06x)\n", manti_out, manti_out);
    // }
    // if (manti_cmt_s == 0) {
    //     return 0; // Return FP32 representation of 0.0
    // }
    // out_algn = (sign << 31) + ((exp_out & 0xff) << 23) + manti_out;
    /*sign 是 uint8_t 类型，但在进行左移操作时，C/C++会首先将其提升为 int 类型（整型提升）
在大多数平台上，int 是32位的
当对 int 类型进行左移31位时，结果可能超出 int 能表示的范围
根据C/C++标准，这种情况下的行为是未定义的

// 方案1：使用uint32_t进行移位操作
out_algn = ((uint32_t)sign << 31) + ((exp_out & 0xff) << 23) + manti_out;

// 或者方案2：直接使用位运算
out_algn = (sign ? 0x80000000 : 0) + ((exp_out & 0xff) << 23) + manti_out;
 */
    out_algn = (sign ? 0x80000000 : 0) + ((exp_out & 0xff) << 23) + manti_out;
    // printf(" algn_out: 0x%08x\n", out_algn);

    return out_algn;
}
