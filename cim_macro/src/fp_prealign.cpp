/**
 * @file fp_prealign_optimized.cpp
 * @brief Optimized floating-point pre-alignment implementation
 * 
 * This implementation follows the specification in floating_point_pre_alignment.md
 * and provides optimized processing for FP16, BF16, FP8E4M3, and FP8E5M2 formats.
 */

#include "../inc/fp_prealign.hpp"
#include <algorithm>
#include <cstring>

// Data type enumerations (matching dcim_com.h exactly)
extern "C" {
    typedef enum {
        INT4 = 0,   INT8 = 1,   INT12 = 2,  INT16 = 3,  FP32 = 4,
        FP16 = 5,   BF16 = 6,   BBF16 = 7,  FP8E4 = 8,  FP8E5 = 9
    } data_type_t;
}

namespace fp_prealign {

// Processing constants
static constexpr size_t VECTOR_SIZE = 32;
static constexpr size_t OUTPUT_SIZE = 33;

/**
 * @brief Optimized floating-point pre-alignment processor
 * 
 * Implements the complete workflow from floating_point_pre_alignment.md:
 * 1. Field extraction (sign, exponent, mantissa)
 * 2. Exponent regularization 
 * 3. Public exponent calculation
 * 4. Mantissa alignment with hidden bit insertion
 * 5. Two's complement conversion for negative values
 * 6. Format-specific output generation
 */
class OptimizedProcessor {
public:
    /**
     * @brief Extract and regularize exponents (Steps 2-3 from spec)
     */
    template<typename FloatType>
    static uint8_t process_exponents(const uint16_t* input_data, uint8_t* reg_exponents) {
        uint8_t max_exponent = 0;

        for (size_t i = 0; i < VECTOR_SIZE; ++i) {
            FloatType fp_value(input_data[i]);
            uint8_t raw_exp = fp_value.exponent();
            
            // Regularization: denormalized (E=0) -> reg_exp=1, normalized -> reg_exp=E
            reg_exponents[i] = fp_value.is_exponent_zero() ? 1 : raw_exp;
            max_exponent = std::max(max_exponent, reg_exponents[i]);
        }
        return max_exponent;
    }

    /**
     * @brief Construct mantissa with hidden bit (Step 4 from spec)
     */
    template<typename FloatType>
    static uint16_t construct_mantissa_with_hidden_bit(const FloatType& fp_value) {
        uint16_t mantissa = fp_value.mantissa();
        uint16_t hidden_bit = fp_value.is_exponent_zero() ? 0 : 1;

        if constexpr (std::is_same_v<FloatType, FP16>) {
            // FP16: H at bit 14, mantissa at bits 13:4
            return (hidden_bit << 14) | (mantissa << 4);
        } else if constexpr (std::is_same_v<FloatType, BF16>) {
            // BF16: H at bit 14, mantissa at bits 13:7  
            return (hidden_bit << 14) | (mantissa << 7);
        } else if constexpr (std::is_same_v<FloatType, FP8E4>) {
            // FP8E4: H at bit 14, mantissa at bits 13:11
            return (hidden_bit << 14) | (mantissa << 11);
        } else if constexpr (std::is_same_v<FloatType, FP8E5>) {
            // FP8E5: H at bit 14, mantissa at bits 13:12
            return (hidden_bit << 14) | (mantissa << 12);
        }
        return 0;
    }

    /**
     * @brief Align mantissa by right-shifting (Step 5 from spec)
     */
    static uint16_t align_mantissa(uint16_t mantissa_with_hidden, uint8_t shift_amount) {
        return (shift_amount >= 15) ? 0 : (mantissa_with_hidden >> shift_amount);
    }

    /**
     * @brief Convert to two's complement for negative numbers (Step 6 from spec)
     * 
     * Matches C implementation exactly: invert 15 bits and add 1
     */
    static uint16_t apply_twos_complement(uint16_t aligned_mantissa, bool is_negative) {
        if (!is_negative) return aligned_mantissa;

        // Two's complement: invert 15 bits and add 1 (matches C implementation)
        uint16_t inverted = 0;
        for (int j = 0; j < 15; ++j) {
            if (!((aligned_mantissa >> j) & 1)) {
                inverted |= (1 << j);
            }
        }
        return inverted + 1;
    }

    /**
     * @brief Generate format-specific output (Step 7 from spec)
     */
    template<typename FloatType>
    static uint16_t generate_output(uint16_t twos_complement_mantissa, bool is_negative, 
                                    uint8_t shift_amount) {
        uint16_t sign_bit = is_negative ? 1 : 0;

        if constexpr (std::is_same_v<FloatType, FP8E4> || std::is_same_v<FloatType, FP8E5>) {
            // FP8 formats: 8-bit output with special overflow handling
            // C implementation: data_algn[i] = (uint8_t)((sbit<<7) + ((reg_manti>>8)&0x7f))
            // Special case: if complete right shift or overflow, result = 0
            if (shift_amount >= 15 || twos_complement_mantissa == 0x8000) {
                return 0;
            }
            return (uint8_t)((sign_bit << 7) + ((twos_complement_mantissa >> 8) & 0x7f));
        } else {
            // FP16/BF16 formats: 16-bit output with overflow handling
            // C implementation: data_algn[i] = (sbit<<15) + reg_manti
            // Overflow behavior: 0x8000 + 0x8000 = 0x10000 -> 0x0000 (16-bit)
            return (sign_bit << 15) + twos_complement_mantissa;
        }
    }
};

/**
 * @brief Main optimized pre-alignment function template
 * 
 * Implements the complete workflow from floating_point_pre_alignment.md
 * with optimizations for each floating-point format.
 */
template<typename FloatType>
void optimized_float_prealign(const uint16_t* input_data, uint16_t* output_data) {
    // Step 1: Extract and regularize exponents
    uint8_t reg_exponents[VECTOR_SIZE];
    uint8_t max_exponent = OptimizedProcessor::process_exponents<FloatType>(input_data, reg_exponents);
    
    // Step 2: Calculate public exponent (subtract bias)
    uint8_t public_exponent = max_exponent - FloatType::bias();
    
    // Step 3: Process each mantissa
    for (size_t i = 0; i < VECTOR_SIZE; ++i) {
        FloatType fp_value(input_data[i]);
        
        // Step 3a: Construct mantissa with hidden bit
        uint16_t mantissa_with_hidden = 
            OptimizedProcessor::construct_mantissa_with_hidden_bit(fp_value);
        
        // Step 3b: Calculate shift amount and align mantissa
        uint8_t shift_amount = max_exponent - reg_exponents[i];
        uint16_t aligned_mantissa = 
            OptimizedProcessor::align_mantissa(mantissa_with_hidden, shift_amount);
        
        // Step 3c: Apply two's complement for negative numbers
        bool is_negative = fp_value.sign() != 0;
        uint16_t twos_complement_mantissa = 
            OptimizedProcessor::apply_twos_complement(aligned_mantissa, is_negative);
        
        // Step 3d: Generate format-specific output
        output_data[i] = OptimizedProcessor::generate_output<FloatType>(
            twos_complement_mantissa, is_negative, shift_amount);
    }
    
    // Step 4: Store public exponent in final position
    output_data[OUTPUT_SIZE - 1] = public_exponent;
}

// Explicit template instantiations for supported formats
// Explicit template instantiations for supported formats
template void optimized_float_prealign<FP16>(const uint16_t*, uint16_t*);
template void optimized_float_prealign<BF16>(const uint16_t*, uint16_t*);
template void optimized_float_prealign<FP8E4>(const uint16_t*, uint16_t*);
template void optimized_float_prealign<FP8E5>(const uint16_t*, uint16_t*);

// Legacy compatibility - redirect to optimized implementation
template<typename FloatType>
void float_prealign(const uint16_t* input_data, uint16_t* output_data) {
    optimized_float_prealign<FloatType>(input_data, output_data);
}

template void float_prealign<FP16>(const uint16_t*, uint16_t*);
template void float_prealign<BF16>(const uint16_t*, uint16_t*);
template void float_prealign<FP8E4>(const uint16_t*, uint16_t*);
template void float_prealign<FP8E5>(const uint16_t*, uint16_t*);

} // namespace fp_prealign

// C-compatible wrapper functions (no fallback dependencies)
extern "C" {

/**
 * @brief Optimized C wrapper for floating-point data alignment
 * 
 * Supports all floating-point formats without fallback dependencies.
 * Throws error for unsupported formats instead of falling back.
 */
void float_data_align_cpp(uint16_t *data_hex, uint8_t data_type, uint16_t *data_algn) {
    switch (data_type) {
        case FP16:
            fp_prealign::optimized_float_prealign<fp_prealign::FP16>(data_hex, data_algn);
            break;
        case BF16:
        case BBF16:  // BBF16 uses same processing as BF16
            fp_prealign::optimized_float_prealign<fp_prealign::BF16>(data_hex, data_algn);
            break;
        case FP8E4:
            fp_prealign::optimized_float_prealign<fp_prealign::FP8E4>(data_hex, data_algn);
            break;
        case FP8E5:
            fp_prealign::optimized_float_prealign<fp_prealign::FP8E5>(data_hex, data_algn);
            break;
        default:
            // Clear output and set error indicator instead of fallback
            memset(data_algn, 0, fp_prealign::OUTPUT_SIZE * sizeof(uint16_t));
            data_algn[fp_prealign::OUTPUT_SIZE - 1] = 0xFFFF;  // Error indicator
            break;
    }
}

/**
 * @brief Optimized FP16/BF16 alignment function
 */
void data_align_f16b_cpp(uint16_t *data_hex, uint8_t data_type, uint16_t *data_algn) {
    if (data_type == FP16) {
        fp_prealign::optimized_float_prealign<fp_prealign::FP16>(data_hex, data_algn);
    } else if (data_type == BF16 || data_type == BBF16) {
        fp_prealign::optimized_float_prealign<fp_prealign::BF16>(data_hex, data_algn);
    } else {
        // Error handling instead of fallback
        memset(data_algn, 0, fp_prealign::OUTPUT_SIZE * sizeof(uint16_t));
        data_algn[fp_prealign::OUTPUT_SIZE - 1] = 0xFFFF;
    }
}

/**
 * @brief Optimized FP8 alignment function  
 */
void data_align_f8b_cpp(uint16_t *data_hex, uint8_t data_type, uint16_t *data_algn) {
    if (data_type == FP8E4) {
        fp_prealign::optimized_float_prealign<fp_prealign::FP8E4>(data_hex, data_algn);
    } else if (data_type == FP8E5) {
        fp_prealign::optimized_float_prealign<fp_prealign::FP8E5>(data_hex, data_algn);
    } else {
        // Error handling instead of fallback
        memset(data_algn, 0, fp_prealign::OUTPUT_SIZE * sizeof(uint16_t));
        data_algn[fp_prealign::OUTPUT_SIZE - 1] = 0xFFFF;
    }
}

} // extern "C"
