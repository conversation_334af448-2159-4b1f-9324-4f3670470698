#include "../inc/fp_prealign.hpp"
#include <algorithm>
#include <cstring>

#ifdef __cplusplus
extern "C" {
#endif
#include "../inc/dcim_com.h"
#ifdef __cplusplus
}
#endif

namespace fp_prealign {

// Constants for pre-alignment processing
static constexpr size_t VECTOR_SIZE = 32;
static constexpr size_t OUTPUT_SIZE = 33;

/**
 * @brief Pre-alignment algorithm implementation
 */
class PreAlignmentProcessor {

public:
    /**
     * @brief Extract and process exponents from input data
     * @tparam FloatType Floating-point format type (FP16, BF16, FP8E4, FP8E5)
     * @param input_data Array of 32 input values
     * @param reg_exponents Output array for regularized exponents
     * @return Maximum exponent value
     */
    template<typename FloatType>
    static uint8_t extract_and_process_exponents(const uint16_t* input_data,
                                                  uint8_t* reg_exponents) {
        uint8_t max_exponent = 0;

        for (size_t i = 0; i < VECTOR_SIZE; ++i) {
            FloatType fp_value(input_data[i]);

            // Extract raw exponent
            uint8_t raw_exponent = fp_value.exponent();

            // Apply regularization logic from specification:
            // if (E == 0): reg_exp = 1 (for denormalized numbers)
            // else: reg_exp = E (for normalized numbers)
            if (fp_value.is_exponent_zero()) {
                reg_exponents[i] = 1;  // Force to 1 for denormalized
            } else {
                reg_exponents[i] = raw_exponent;
            }

            // Track maximum exponent for alignment
            if (reg_exponents[i] > max_exponent) {
                max_exponent = reg_exponents[i];
            }
        }

        return max_exponent;
    }

    /**
     * @brief Calculate public exponent by subtracting bias
     * @tparam FloatType Floating-point format type
     * @param max_exponent Maximum exponent from input data
     * @return Public exponent (max_exponent - bias)
     */
    template<typename FloatType>
    static uint8_t calculate_public_exponent(uint8_t max_exponent) {
        return max_exponent - FloatType::bias();
    }

    /**
     * @brief Calculate shift amounts for mantissa alignment
     * @param max_exponent Maximum exponent value
     * @param reg_exponents Array of regularized exponents
     * @param shift_amounts Output array for shift amounts
     */
    static void calculate_shift_amounts(uint8_t max_exponent,
                                        const uint8_t* reg_exponents,
                                        uint8_t* shift_amounts) {
        for (size_t i = 0; i < VECTOR_SIZE; ++i) {
            shift_amounts[i] = max_exponent - reg_exponents[i];
        }
    }

    /**
     * @brief Construct mantissa with hidden bit for alignment
     * @tparam FloatType Floating-point format type
     * @param fp_value Floating-point value
     * @return Mantissa with hidden bit inserted at appropriate position
     */
    template<typename FloatType>
    static uint16_t construct_mantissa_with_hidden_bit(const FloatType& fp_value) {
        uint16_t mantissa = fp_value.mantissa();
        uint8_t hidden_bit = fp_value.hidden_bit();

        // Position hidden bit according to format specifications
        if constexpr (std::is_same_v<FloatType, FP16>) {
            // FP16: H at bit 14, mantissa at bits 13:4
            return (hidden_bit << 14) | (mantissa << 4);
        } else if constexpr (std::is_same_v<FloatType, BF16>) {
            // BF16: H at bit 14, mantissa at bits 13:7
            return (hidden_bit << 14) | (mantissa << 7);
        } else if constexpr (std::is_same_v<FloatType, FP8E4>) {
            // FP8E4: H at bit 14, mantissa at bits 13:11
            return (hidden_bit << 14) | (mantissa << 11);
        } else if constexpr (std::is_same_v<FloatType, FP8E5>) {
            // FP8E5: H at bit 14, mantissa at bits 13:12
            return (hidden_bit << 14) | (mantissa << 12);
        }

        return 0;  // Should never reach here
    }

    /**
     * @brief Perform right-shift alignment of mantissa
     * @param mantissa_with_hidden Mantissa with hidden bit
     * @param shift_amount Number of positions to shift right
     * @return Aligned mantissa
     */
    static uint16_t align_mantissa(uint16_t mantissa_with_hidden, uint8_t shift_amount) {
        // Perform arithmetic right shift to preserve sign extension
        return mantissa_with_hidden >> shift_amount;
    }

    /**
     * @brief Convert to two's complement for negative numbers (matches C implementation exactly)
     * @param aligned_mantissa Aligned mantissa value
     * @param is_negative True if the number is negative
     * @return Two's complement representation
     */
    static uint16_t convert_to_twos_complement(uint16_t aligned_mantissa, bool is_negative) {
        if (!is_negative) {
            return aligned_mantissa;
        }

        // Two's complement: invert all bits (only 15 bits) and add 1
        // This matches the C implementation exactly
        uint16_t bit_tmp = 0;
        for (int j = 0; j < 15; ++j) {
            bit_tmp += ((!((aligned_mantissa >> j) & 1)) << j);
        }
        return bit_tmp + 1;
    }
};

/**
 * @brief Main pre-alignment function template
 * @tparam FloatType Floating-point format type (FP16, BF16, FP8E4, FP8E5)
 * @param input_data Array of 32 input values in uint16_t format
 * @param output_data Array of 33 output values (32 aligned mantissas + 1 public exponent)
 */
template<typename FloatType>
void float_prealign(const uint16_t* input_data, uint16_t* output_data) {
    // Step 1: Extract and process exponents
    uint8_t reg_exponents[VECTOR_SIZE];
    uint8_t max_exponent = PreAlignmentProcessor::extract_and_process_exponents<FloatType>(
        input_data, reg_exponents);

    // Step 2: Calculate public exponent
    uint8_t public_exponent = PreAlignmentProcessor::calculate_public_exponent<FloatType>(max_exponent);

    // Step 3: Calculate shift amounts
    uint8_t shift_amounts[VECTOR_SIZE];
    PreAlignmentProcessor::calculate_shift_amounts(max_exponent, reg_exponents, shift_amounts);

    // Step 4: Process each input value
    for (size_t i = 0; i < VECTOR_SIZE; ++i) {
        FloatType fp_value(input_data[i]);

        // Step 4a: Construct mantissa with hidden bit
        uint16_t mantissa_with_hidden =
            PreAlignmentProcessor::construct_mantissa_with_hidden_bit<FloatType>(fp_value);

        // Step 4b: Align mantissa by right-shifting
        uint16_t aligned_mantissa =
            PreAlignmentProcessor::align_mantissa(mantissa_with_hidden, shift_amounts[i]);

        // Step 4c: Convert to two's complement if negative
        bool is_negative = fp_value.sign() != 0;
        uint16_t twos_complement_mantissa =
            PreAlignmentProcessor::convert_to_twos_complement(aligned_mantissa, is_negative);

        // Step 4d: Store result exactly like C implementation
        // C code: data_algn[i] = (sbit<<15) + reg_manti;
        uint16_t sign_bit = is_negative ? 1 : 0;
        output_data[i] = (sign_bit << 15) + twos_complement_mantissa;
    }

    // Step 5: Store public exponent in the 33rd element
    output_data[OUTPUT_SIZE - 1] = public_exponent;
}

/**
 * @brief Explicit template instantiations for supported formats
 */
template void float_prealign<FP16>(const uint16_t* input_data, uint16_t* output_data);
template void float_prealign<BF16>(const uint16_t* input_data, uint16_t* output_data);
template void float_prealign<FP8E4>(const uint16_t* input_data, uint16_t* output_data);
template void float_prealign<FP8E5>(const uint16_t* input_data, uint16_t* output_data);

} // namespace fp_prealign

// C-compatible wrapper functions for integration with existing codebase
extern "C" {

/**
 * @brief C wrapper for floating-point data alignment (enhanced version)
 * @param data_hex Input array of 32 uint16_t values
 * @param data_type Data type enum (FP16, BF16, FP8E4, FP8E5)
 * @param data_algn Output array of 33 uint16_t values (32 aligned + 1 public exponent)
 */
void float_data_align_cpp(uint16_t *data_hex, uint8_t data_type, uint16_t *data_algn) {
    switch (data_type) {
        case FP16:
            fp_prealign::float_prealign<fp_prealign::FP16>(data_hex, data_algn);
            break;
        case BF16:
            fp_prealign::float_prealign<fp_prealign::BF16>(data_hex, data_algn);
            break;
        case FP8E4:
            fp_prealign::float_prealign<fp_prealign::FP8E4>(data_hex, data_algn);
            break;
        case FP8E5:
            fp_prealign::float_prealign<fp_prealign::FP8E5>(data_hex, data_algn);
            break;
        case BBF16:
            // BBF16 uses the same processing as BF16 but with different output format
            fp_prealign::float_prealign<fp_prealign::BF16>(data_hex, data_algn);
            break;
        default:
            // Fallback to original implementation for unsupported types
            float_data_align(data_hex, data_type, data_algn);
            break;
    }
}

/**
 * @brief Enhanced FP16/BF16 alignment function (C++ implementation)
 * @param data_hex Input array of 32 uint16_t values
 * @param data_type Data type enum (FP16 or BF16)
 * @param data_algn Output array of 33 uint16_t values
 */
void data_align_f16b_cpp(uint16_t *data_hex, uint8_t data_type, uint16_t *data_algn) {
    if (data_type == FP16) {
        fp_prealign::float_prealign<fp_prealign::FP16>(data_hex, data_algn);
    } else if (data_type == BF16) {
        fp_prealign::float_prealign<fp_prealign::BF16>(data_hex, data_algn);
    } else {
        // Fallback to original implementation
        data_align_f16b(data_hex, data_type, data_algn);
    }
}

/**
 * @brief Enhanced FP8 alignment function (C++ implementation)
 * @param data_hex Input array of 32 uint16_t values
 * @param data_type Data type enum (FP8E4 or FP8E5)
 * @param data_algn Output array of 33 uint16_t values
 */
void data_align_f8b_cpp(uint16_t *data_hex, uint8_t data_type, uint16_t *data_algn) {
    if (data_type == FP8E4) {
        fp_prealign::float_prealign<fp_prealign::FP8E4>(data_hex, data_algn);
    } else if (data_type == FP8E5) {
        fp_prealign::float_prealign<fp_prealign::FP8E5>(data_hex, data_algn);
    } else {
        // Fallback to original implementation
        data_align_f8b(data_hex, data_type, data_algn);
    }
}

} // extern "C"