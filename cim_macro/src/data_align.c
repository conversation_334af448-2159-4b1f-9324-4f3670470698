#include "../inc/dcim_com.h"

void float_data_align(uint16_t *data_hex, uint8_t data_type, uint16_t *data_algn) {
  if((data_type == FP16) || (data_type == BF16)) {
    data_align_f16b(data_hex, data_type, data_algn);
  }
  else if(data_type == BBF16) {
    data_align_bbf16b(data_hex, data_type, data_algn);
  }
  else if((data_type == FP8E5) || (data_type == FP8E4)) {
    data_align_f8b(data_hex, data_type, data_algn);
  }
}

// FP16/BF16
void data_align_f16b(uint16_t *data_hex, uint8_t data_type, uint16_t *data_algn) {

  uint8_t  bias = 0;
  uint8_t  ebits = 0;
  uint8_t  reg_exp[32] = {0};
  uint8_t  emax_src, emax_cmp = 0;
  uint16_t reg_manti = 0;
  uint16_t bit_tmp = 0;
  uint8_t  sbit = 0;
  uint8_t  hbit = 0;

  switch(data_type) {
    case  FP16:
    case FP8E5: bias = 15;  break;
    case  BF16:
    case BBF16: bias = 127; break;
    case FP8E4: bias = 7;   break;
  }

  emax_src = 0;
  for(int i=0;i<32;i++) {

    reg_exp[i] = 0;
    if(data_type == FP16) {
      ebits = bit_acc(10,14,data_hex[i]);
    }
    else {  // BF16
      ebits = bit_acc(7,14,data_hex[i]);
    }
    hbit = (ebits>0) ? 1 : 0;
    reg_exp[i] = ebits&0xfe;
    reg_exp[i] += hbit ? (ebits&1) : 1;

    if(reg_exp[i] > emax_src)
      emax_src = reg_exp[i];

  }
  emax_cmp = emax_src - bias;
  // printf("EMAX -> emax_src: 0x%02x emax_cmp: 0x%02x\n", emax_src, emax_cmp);

  for(int i=0;i<32;i++) {

    reg_manti = 0;
    hbit = (bit_acc(10,14,data_hex[i])>0) ? 1 : 0;
    reg_manti += (hbit<<14);
    if(data_type == FP16) {
      bit_tmp = bit_acc(0,9,data_hex[i]);
      reg_manti += (bit_tmp<<4);
    }
    else {
      bit_tmp = bit_acc(0,6,data_hex[i]);
      reg_manti += (bit_tmp<<7);
    }
    reg_manti = (reg_manti>>(emax_src-reg_exp[i]));
    sbit = bit_acc(15,15,data_hex[i]);

    if(sbit) {
      bit_tmp = 0;
      for(int j=0;j<15;j++) {
        bit_tmp += ((!((reg_manti>>j)&1))<<j);
      }
      reg_manti = bit_tmp+1;
    }

    data_algn[i] = (sbit<<15) + reg_manti;

    //printf("reg_manti[%02d]: 0x%04x -> 0x%04x\n", i, ((sbit<<15)+reg_manti), data_algn[i]);
  }
  data_algn[32] = emax_cmp;
}


void data_align_bbf16b(uint16_t *data_hex, uint8_t data_type, uint16_t *data_algn) {

  uint8_t  bias = 0;
  uint8_t  ebits = 0;
  uint8_t  reg_exp[32] = {0};
  uint8_t  emax_src, emax_cmp = {0};

  uint16_t reg_manti = 0;
  uint16_t bit_tmp = 0;
  uint8_t  sbit = 0;
  uint8_t  hbit = 0;

  switch(data_type) {
    case  FP16:
    case FP8E4: bias = 15;  break;
    case  BF16:
    case BBF16: bias = 127; break;
    case FP8E5: bias = 7;   break;
  }

  emax_src = 0;
  for(int i=0;i<32;i++) {

    ebits = bit_acc(7,14,data_hex[i]);
    hbit = (ebits>0) ? 1 : 0;

    reg_exp[i] = 0;
    reg_exp[i] = ebits & 0xfe;
    reg_exp[i] += hbit ? (ebits&1) : 1;

    if(reg_exp[i] > emax_src)
      emax_src = reg_exp[i];

  }
  emax_cmp = emax_src - bias;
  // printf("EMAX -> emax_src: 0x%02x emax_cmp: 0x%02x\n", emax_src, emax_cmp);


  for(int i=0;i<32;i++) {

    reg_manti = 0;
    hbit = (bit_acc(7,14,data_hex[i])>0) ? 1 : 0;
    reg_manti += (hbit<<14);
    bit_tmp = bit_acc(1,6,data_hex[i]);
    reg_manti += (bit_tmp<<8);
    reg_manti = (reg_manti>>(emax_src-reg_exp[i]));
    sbit = bit_acc(15,15,data_hex[i]);

    if(sbit) {
      bit_tmp = 0;
      for(int j=0;j<15;j++) {
        bit_tmp += ((!((reg_manti>>j)&1))<<j);
      }
      reg_manti = bit_tmp+1;
    }

    data_algn[i] = (uint8_t)((sbit<<7) + ((reg_manti>>8)&0x7f));
    //data_algn[i] = (sbit<<7) + ((reg_manti>>8)&0x7f);
    //data_algn[i] = (((sbit<<15)+(reg_manti&0x7fff))>>8);
    //data_algn[i] = ((sbit<<15)+(reg_manti&0x7fff));
    //
    //if(sbit)
    //  data_algn[i] += (0xff<<8);

    if(((emax_src-reg_exp[i])>=15) || (reg_manti==0x8000))
      data_algn[i] = 0;
    //printf(" reg_matin[%02d]: %04x -> %04x\n", i, data_hex[i], data_algn[i]);

  }
  data_algn[32] = emax_cmp;
}


void data_align_f8b(uint16_t *data_hex, uint8_t data_type, uint16_t *data_algn) {

  uint8_t  bias = 0;
  uint8_t  ebits = 0;
  uint8_t  reg_exp[32] = {0};
  uint8_t  emax_src, emax_cmp = {0};

  uint16_t reg_manti = 0;
  uint16_t bit_tmp = 0;
  uint8_t  sbit = 0;
  uint8_t  hbit = 0;

  switch(data_type) {
    case  FP16:
    case FP8E5: bias = 15;  break;
    case  BF16:
    case BBF16: bias = 127; break;
    case FP8E4: bias = 7;   break;
  }

  emax_src = 0;
  for(int i=0;i<32;i++) {

    if(data_type == FP8E4) {
      ebits = bit_acc(3,6,data_hex[i]);
    }
    else {
      ebits = bit_acc(2,6,data_hex[i]);
    }
    hbit = (ebits>0) ? 1 : 0;

    reg_exp[i] = 0;
    reg_exp[i] = ebits & 0xfe;
    reg_exp[i] += hbit ? (ebits&1) : 1;

    if(reg_exp[i] > emax_src)
      emax_src = reg_exp[i];

  }
  emax_cmp = emax_src - bias;
  // printf("EMAX -> emax_src: 0x%02x emax_cmp: 0x%02x \n", emax_src, emax_cmp);


  for(int i=0;i<32;i++) {

    reg_manti = 0;
    if(data_type == FP8E4) {
      hbit = (bit_acc(3,6,data_hex[i])>0) ? 1 : 0;
      reg_manti += (hbit<<14);
      bit_tmp = bit_acc(0,2,data_hex[i]);
      reg_manti += (bit_tmp<<11);
    }
    else {
      hbit = (bit_acc(2,6,data_hex[i])>0) ? 1 : 0;
      reg_manti += (hbit<<14);
      bit_tmp = bit_acc(0,1,data_hex[i]);
      reg_manti += (bit_tmp<<12);
    }
    //printf("%04x >> ", reg_manti);
    reg_manti = (reg_manti>>(emax_src-reg_exp[i]));
    //printf("%02x = %04x -> ", (emax_src-reg_exp[i]), reg_manti);
    sbit = bit_acc(7,7,data_hex[i]);

    if(sbit) {
      bit_tmp = 0;
      for(int j=0;j<15;j++) {
        bit_tmp += ((!((reg_manti>>j)&1))<<j);
      }
      reg_manti = bit_tmp+1;
    }
    //printf("%04x =>", reg_manti);

    data_algn[i] = (uint8_t)((sbit<<7) + ((reg_manti>>8)&0x7f));
    if(((emax_src-reg_exp[i])>=15) || (reg_manti==0x8000))
      data_algn[i] = 0;
    //printf("%04x \n", data_algn[i]);

    //printf("reg_manti[%02d]: 0x%04x -> 0x%04x\n", i, data_hex[i], data_algn[i]);
  }
  data_algn[32] = emax_cmp;
}


