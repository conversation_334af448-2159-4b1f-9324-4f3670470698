#include "../inc/dcim_com.h"

uint32_t bit_acc(uint8_t sta_bit, uint8_t stp_bit, uint32_t dst_data) {
  uint8_t bit_len = stp_bit-sta_bit+1;
  uint32_t out_data = (dst_data>>sta_bit)&((1<<bit_len)-1);

  if((stp_bit-sta_bit)<0) {
    printf("ERROR!!!(start bit > stop bit)\n");
    return 0;
  }

/***
  printf("msb:-");
  for(int i=0;i<32;i++) {
    printf("%0d", (dst_data>>(31-i))&1);
    if((i&3)==3)
      printf("-");
  }
  printf("\n");

  printf("msb:-");
  for(int i=0;i<32;i++) {
    if((i>31-sta_bit) || (i<31-stp_bit))
      printf("*");
    else
      printf("%0d", (dst_data>>(31-i))&1);
    if((i&3)==3)
      printf("-");
  }
  printf("\n");
***/
  return out_data;
}

