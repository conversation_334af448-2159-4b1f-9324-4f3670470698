add_rules("mode.debug","mode.coverage")
set_project("npu_sc")
add_rules("plugin.compile_commands.autoupdate", {outputdir = ".vscode"})
add_requires("magic_enum 0.9.6")
add_requires("gtest 1.15.2")
add_requires("nlohmann_json 3.11.3")
add_requires("spdlog 1.15.2")
-- set_languages("cxx11")
set_toolchains("clang-18")
set_policy("build.sanitizer.address", true)
set_policy("build.sanitizer.undefined", true)
set_policy("build.sanitizer.leak", true)

set_warnings("all", "extra", "pedantic")


target("ac_types")
    add_includedirs("ac_types-master/include", {public = true})
    set_kind("static")


includes("cim_macro/xmake.lua")




