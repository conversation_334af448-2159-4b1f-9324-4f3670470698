# 浮点预对齐处理文档

## 概述

浮点预对齐是一种将不同格式的浮点数统一处理的技术，用于在矢量计算中高效处理混合精度浮点运算。该技术将多种浮点格式（FP16、BF16、FP8等）转换为统一的内部表示格式。

## 支持的浮点格式

### 1. FP16 (Half Precision)
- **总位宽**: 16 bits
- **符号位**: 1 bit
- **指数位**: 5 bits (偏置值: 15)
- **尾数位**: 10 bits
- **范围**: 指数范围 [-14, 15]

### 2. BF16 (Brain Float 16)
- **总位宽**: 16 bits  
- **符号位**: 1 bit
- **指数位**: 8 bits (偏置值: 127)
- **尾数位**: 7 bits
- **范围**: 指数范围 [-126, 127]

### 3. FP8 E5M2 (5位指数，2位尾数)
- **总位宽**: 8 bits
- **符号位**: 1 bit
- **指数位**: 5 bits (偏置值: 15)
- **尾数位**: 2 bits
- **范围**: 指数范围 [-14, 15]

### 4. FP8 E4M3 (4位指数，3位尾数)
- **总位宽**: 8 bits
- **符号位**: 1 bit
- **指数位**: 4 bits (偏置值: 7)
- **尾数位**: 3 bits
- **范围**: 指数范围 [-6, 7]

## 预对齐处理流程

### 步骤1: 输入格式统一化

所有输入数据统一扩展为16位格式：

```
- FP16: 直接使用16位
- BF16: 直接使用16位
- FP8: 放置在低8位，高8位补0
```

### 步骤2: 字段提取

从统一的16位输入中提取各个字段：

#### 2.1 符号位提取 (S)
- FP16/BF16: bit[15]
- FP8: bit[7]

#### 2.2 指数位提取 (E)
- FP16: bits[14:10] (5位)
- BF16: bits[14:7] (8位)
- FP8 E5M2: bits[6:2] (5位)
- FP8 E4M3: bits[6:3] (4位)

#### 2.3 尾数位提取 (M)
- FP16: bits[9:0] (10位)
- BF16: bits[6:0] (7位)
- FP8 E5M2: bits[1:0] (2位)
- FP8 E4M3: bits[2:0] (3位)

### 步骤3: 隐含位处理

```verilog
// 规范化指数值，用于最大值比较与移位计算
if (E == 0) {
    reg_exp = 8'b0000_0001;  // 非规格化数：指数整体置 1
    H       = 1'b0;         // 隐含位为 0
} else {
    reg_exp = E;            // 规格化数：指数保持不变
    H       = 1'b1;         // 隐含位为 1
}
```

**特殊处理说明**：
- 当检测到非规格化数 (`E == 0`) 时，将 `reg_exp` 强制设为 1，而不是简单改写最低 bit。
- 这样可以保证非规格化数在后续最大指数比较时不会被当成 0 处理，同时保留正确的隐含位。

### 步骤4: 隐含位生成

```verilog
H = exp;  // 隐含位等于原始指数值
```

对于规格化数，隐含位为1；对于非规格化数，隐含位为0。

### 步骤5: 公共指数计算

#### 5.1 寻找最大指数
在32个输入的处理后指数中找到最大值：
```
e_max = max(reg_exp[31:0])
```

#### 5.2 减去偏置
```
public_exponent = e_max - bias
```

其中bias为对应数据类型的偏置值。

### 步骤6: 尾数对齐

#### 6.1 计算移位量
```
shift_amount = e_max - reg_exp[i]  // 对每个输入i
```

#### 6.2 构造待移位数据
```
mantissa_with_hidden = {H, M}  // 隐含位 + 尾数位
```

#### 6.3 右移对齐
```
aligned_mantissa = mantissa_with_hidden >> shift_amount
```

### 步骤7: 输出格式

最终结果存入 `array<uint16_t, 33>  vector_int_o`，共 **33** 个 `uint16_t` 元素：

1. **vector_int_o[0 .. 31]**  – 对齐后的尾数（补码形式）
   - [15] 符号位 (S)
   - [14:0] 对齐后的尾数
   - 对于 FP8 格式，高位不足的位置在硬件中自动补 0。

2. **vector_int_o[32]** – 公共指数 `e_max`
   - 低 8 位为有效指数值 (已减偏置)
   - 高 8 位清零

这样后端硬件只需要读取 33×16bit 的定长向量即可完成后续乘法流程。

### 4. 输出格式说明

最终输出的 `vector_int_o` 统一为 `uint16_t`，格式如下：

```
vector_int_o[0..31] : {S , aligned_mantissa[14:0]}  // 尾数补码
vector_int_o[32]    : {8'b0 , e_max[7:0]}           // 公共指数
```

补码形式与统一 16-bit 容器便于与后端定点乘法单元直接对接。

## 处理示例

### FP16输入示例
```
输入: 0x4200 (FP16格式的3.0)
- 符号位: 0
- 指数: 10000 (16) -> 减偏置后为1
- 尾数: 1000000000

处理过程:
1. 隐含位H = 1 (规格化数)
2. 如果这是最大指数，则e_max = 16
3. 移位量 = 0 (自己就是最大)
4. 输出尾数: {1, 1000000000} = 1100000000000000
```

### FP8输入示例
```
输入: 0x42 (FP8 E4M3格式)
- 符号位: 0  
- 指数: 1000 (8) -> 减偏置7后为1
- 尾数: 010

处理过程:
1. 扩展到16位: 0x0042
2. 隐含位H = 1
3. 如果需要右移n位进行对齐
4. 输出对齐后的尾数
```


## 详细处理流程图

```
输入阶段 (32个16位数据)
    │
    ├─ FP16: [S|EEEEE|MMMMMMMMMM] (直接使用)
    ├─ BF16: [S|EEEEEEEE|MMMMMMM] (直接使用)  
    └─ FP8:  [00000000|S|EEEE|MMM] (放在低8位)
    │
    ▼
字段提取阶段
    │
    ├─ 符号位 (S) ────────────────────┐
    ├─ 指数位 (E) ─┐                   │
    └─ 尾数位 (M) ─┼─────────────────┐ │
                   │                 │ │
                   ▼                 │ │
指数处理阶段        │                 │ │
    │              │                 │ │
    ├─ if (E==0): reg_exp[0]=1      │ │
    └─ else: reg_exp=E              │ │
    │                               │ │
    ▼                               │ │
隐含位生成                          │ │
    │                               │ │
    ├─ H = (E==0) ? 0 : 1          │ │
    │                               │ │
    ▼                               │ │
公共指数计算                        │ │
    │                               │ │
    ├─ e_max = max(reg_exp[31:0])  │ │
    ├─ public_exp = e_max - bias    │ │
    │                               │ │
    ▼                               │ │
移位计算                            │ │
    │                               │ │
    ├─ shift[i] = e_max - reg_exp[i] │ │
    │                               │ │
    ▼                               │ │
尾数对齐 ◄──────────────────────────┘ │
    │                                 │
    ├─ mantissa_full = {H, M}         │
    ├─ aligned_mantissa = mantissa_full >> shift[i]
    │                                 │
    ▼                                 │
输出生成 ◄────────────────────────────┘
    │
    ├─ e_max[7:0] (公共指数)
    └─ vector_int_o[31:0][15:0] = {S, aligned_mantissa} (补码形式)
```

## 关键概念详解

### 1. 为什么需要公共指数？

在传统的浮点加法中，需要对每一对数进行对齐，这在SIMD运算中效率很低。通过使用公共指数：
- 所有32个数只需要对齐一次
- 减少了硬件复杂度
- 提高了并行处理效率

### 2. 隐含位处理的特殊逻辑

```verilog
// 关键逻辑：指数最低位的特殊处理
if (E == 0) {
    reg_exp[0] = 1;     // 强制设置最低位为1
    H = 0;              // 隐含位为0（非规格化）
} else {
    reg_exp[0] = E[0];  // 保持原指数最低位
    H = 1;              // 隐含位为1（规格化）
}
```

这样处理的原因：
- 确保非规格化数在对齐时能正确处理
- 简化后续的移位逻辑
- 保持数值精度

### 3. 移位对齐的精度考虑

```
原始尾数: H.MMMMMMM
移位后:   0.0HMMMMMM (右移1位)
         0.00HMMMMM (右移2位)
```

移位过程中：
- 保持符号位不变
- 根据移位量确定最终精度
- 超出位宽的位会被截断

### 4. 输出格式说明

最终输出的`vector_int_o`格式（补码表示）：

**FP16/BF16输入时：**
```
[15]    : 符号位 (S)
[14:0]  : 对齐后的尾数部分
```

**FP8输入时：**
```
[7]     : 符号位 (S)  
[6:0]   : 对齐后的尾数部分
[15:8]  : 不使用（高8位）
```

补码格式便于后续直接进行尾数乘法运算，类似标准浮点乘法流程中的尾数处理。

## 后续计算流程

预对齐处理完成后，后续的计算遵循标准浮点乘法流程：

### 1. 尾数向量乘法
```
result_mantissa = vector_A × vector_B  // 两个32位补码向量相乘
```

### 2. 指数运算
```
result_exponent = public_exp_A + public_exp_B  // 公共指数相加
```

### 3. 结果规格化
- 对乘法结果进行移位调整
- 指数相应增减
- 处理溢出和下溢

### 4. 最终输出
将结果转换回标准IEEE754格式（S-E-M形式）

## Mermaid处理流程图

```mermaid
graph TD
    A["32个输入浮点数<br/>FP16/BF16/FP8"] --> B["格式统一化<br/>扩展为16位"]
    B --> C["字段提取"]
    
    C --> D["符号位(S)"]
    C --> E["指数位(E)"]
    C --> F["尾数位(M)"]
    
    E --> G{"E == 0?"}
    G -->|是| H["reg_exp = 1<br/>H = 0"]
    G -->|否| I["reg_exp = E<br/>H = 1"]
    
    H --> J["公共指数计算"]
    I --> J
    J --> K["e_max = max(reg_exp)"]
    K --> L["public_exp = e_max - bias"]
    
    K --> M["移位量计算"]
    M --> N["shift = e_max - reg_exp[i]"]
    
    N --> O["尾数对齐"]
    F --> O
    H --> O
    I --> O
    O --> P["mantissa_full = {H , M}"]
    P --> Q["aligned_mantissa >> shift"]
    
    Q --> R["补码转换"]
    D --> R
    R --> S["输出生成"]
    L --> S
    
    S --> U["vector_int_o[0..31] = 对齐尾数"]
    S --> T["vector_int_o[32] = 公共指数"]
    
    U --> V["后续浮点乘法"]
    T --> V
    V --> W["尾数乘法"]
    V --> X["指数相加"]
    W --> Y["结果规格化"]
    X --> Y
    Y --> Z["输出 IEEE754 S-E-M"]
```

新的流程图反映了修正后的指数处理方式、33×uint16_t 输出结构，以及先计算最大指数再移位对齐的正确顺序。

## G / Guard 位、R / Reserved 位的含义

为了在一次右移操作中支持 **任意指数差值**，所有格式都统一扩展到 16bit，并在最低位预留若干空位：

| 格式 | 带符号位总宽 | 组成                          | 说明 |
|------|--------------|-------------------------------|------|
| FP16 | 16           | S 1 | H 1 | M 10 | G 4         | G 用来在对齐时容纳被右移出的尾数位，提供舍入信息 |
| BF16 | 16           | S 1 | H 1 | M 7  | G 7         | |
| FP8 E5M2 | 16        | S 1 | H 1 | M 2  | G 4 | R 8   | 尾数仅 2 位，因此需要更多 Guard/Reserved 位 |
| FP8 E4M3 | 16        | S 1 | H 1 | M 3  | G 3 | R 8   | 图中粉红色块为 G，白色块为 R |

- **G (Guard)**：最靠近尾数的若干位。对齐右移过程中被移出的原始尾数会进入 G，用于最终的舍入 (round) 决策。
- **R (Reserved)**：进一步向右的填充 0。它们确保总位宽恒为 16bit，也为 sticky-bit 计算提供空间。

> 只有 FP8 系列需要大量 R，因为其本身尾数短。

## 移位对齐示例

设 `e_max = 10`，某元素 `reg_exp = 7` ，则

```
shift = e_max - reg_exp = 3
```

对齐时执行**算术右移** (`>>>`)：

```
原始: [S][H][MMMMMMMMMM][GGGG]
右移3: [SSS][S][HMM][MMMM][GG]
```

关键点：
1. **符号扩展**：最高位 S 重复灌入左侧，保持负数补码正确。
2. **G 区域**：被移出的最低有效位落到 Guard 区，为后续舍入保留信息。
3. **R 区域 / Sticky**：当移位量大于 G+M，总有位置容纳所有被移出的位，最终 Sticky 位可由 R 的 or-reduce 得到。

## 补码转换

在完成移位后，再执行一次「带符号数 → 二进制补码」转换：

```verilog
if (S == 1'b1) begin
    vector_int = (~aligned_mantissa) + 16'd1;  // 取反加 1
end else begin
    vector_int = aligned_mantissa;             // 正数保持不变
end
```

此时 32 个 `vector_int_o[i]` 都是 **int16_t**，可以直接送入后续定点算子。

## 后续乘法 & 规格化

以下以两个并行流 A、B 举例：

### 1. 尾数乘法 (32 路并行)

```
prod[i] = vector_int_A[i]  * vector_int_B[i];   // 16b × 16b → 32b
```

### 2. 指数计算

```
exp_out = e_max_A + e_max_B;  // 加两个 8bit 公共指数
```

### 3. 结果规格化

1. **检测溢出 / 前导位**
   - 乘法结果最高两位可能是 `01`、`10`、`11` 等。
   - 若最高 2bit 为 `01` / `10`，说明隐藏位正确，无需左移；若为 `00` 或 `11`，执行左移 1bit 并 `exp_out -= 1`。
2. **舍入**
   - 结合 Guard 与 Sticky 位执行最近偶数舍入 (RNE)。
3. **截断到目标尾数位宽**
   - 对 FP16：保留 10 位尾数。
   - 对 FP8 E4M3：保留 3 位尾数。
4. **处理异常** (溢出、下溢、NaN、Inf)。

### 4. 重新打包为 S-E-M

```verilog
out.S = prod_sign;
out.E = exp_out + bias;     // 加回偏置
out.M = rounded_mantissa;
```

这样即可得到完全兼容 IEEE754（或 FP8 规范）的乘法结果。